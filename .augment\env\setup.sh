#!/bin/bash

# Update system packages
sudo apt-get update

# Install Node.js 18 (LTS) if not already installed
if ! command -v node &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Verify Node.js and npm versions
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"

# Navigate to workspace
cd /mnt/persist/workspace

# Install dependencies
echo "Installing project dependencies..."
npm install

# Install Jest and testing dependencies
echo "Installing Jest and testing dependencies..."
npm install --save-dev jest @types/jest jest-environment-jsdom @testing-library/react @testing-library/jest-dom

# Create Jest configuration (fixing the moduleNameMapping typo)
echo "Creating Jest configuration..."
cat > jest.config.js << 'EOF'
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
})

// Add any custom config to be passed to Jest
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  testMatch: [
    '**/__tests__/**/*.(ts|tsx|js)',
    '**/*.(test|spec).(ts|tsx|js)'
  ],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
  ],
}

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig)
EOF

# Create Jest setup file
echo "Creating Jest setup file..."
cat > jest.setup.js << 'EOF'
import '@testing-library/jest-dom'
EOF

# Add test script to package.json if it doesn't exist
echo "Adding test script to package.json..."
npm pkg set scripts.test="jest"
npm pkg set scripts.test:watch="jest --watch"
npm pkg set scripts.test:coverage="jest --coverage"

# Generate Prisma client (required for the project)
echo "Generating Prisma client..."
npx prisma generate

echo "Setup completed successfully!"