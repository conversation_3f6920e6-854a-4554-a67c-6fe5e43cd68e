# =============================================================================
# ENVIRONMENT CONFIGURATION TEMPLATE
# =============================================================================
# 1. Copy this file: cp .env.example .env.local
# 2. Fill in your actual values below
# 3. Never commit .env.local to git
# =============================================================================

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL="postgresql://postgres:your_password@localhost:5432/events?schema=public"
DIRECT_URL="postgresql://postgres:your_password@localhost:5432/events?schema=public"

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
# Generate with: openssl rand -base64 32
AUTH_SECRET="your-auth-secret-here"
NEXTAUTH_SECRET="your-auth-secret-here"
NEXTAUTH_URL="http://localhost:3000"
JWT_SECRET="your-jwt-secret-here"

# =============================================================================
# OAUTH PROVIDERS (Optional)
# =============================================================================
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# =============================================================================
# APPLICATION URLS
# =============================================================================
NEXT_PUBLIC_API_URL="http://localhost:3000/api"
NEXT_PUBLIC_BASE_URL="http://localhost:3000"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_SITE_URL="http://localhost:3000"

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
RESEND_API_KEY="your-resend-api-key"
EMAIL_FROM="<EMAIL>"
EMAIL_TESTING="true"

# =============================================================================
# EXTERNAL APIS (Optional)
# =============================================================================
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY="your-google-maps-api-key"
NEXT_PUBLIC_EXCHANGE_RATE_API_KEY="your-exchange-rate-api-key"
LLAMA_API_KEY="your-llama-api-key"
OPENAI_API_KEY="your-openai-api-key"

# =============================================================================
# PAYMENT PROCESSING (Optional)
# =============================================================================
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
NODE_ENV="development"
TRUST_HOST="true"
NEXT_PUBLIC_AUTO_SEED="true"

# Default admin credentials (development only)
DEFAULT_SUPERADMIN_EMAIL="<EMAIL>"
DEFAULT_SUPERADMIN_PASSWORD="SuperAdmin@123456"
DEFAULT_SUPERADMIN_NAME="System Super Administrator"
DEFAULT_ADMIN_EMAIL="<EMAIL>"
DEFAULT_ADMIN_PASSWORD="Admin@123456"

# =============================================================================
# PRODUCTION OVERRIDES
# =============================================================================
# For production, override these in your deployment environment:
# - NODE_ENV="production"
# - Use secure, randomly generated secrets
# - Use production database URLs
# - Use production API keys
# =============================================================================
