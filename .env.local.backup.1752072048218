# Resend API Key - Replace with your actual API key
RESEND_API_KEY=re_BbuTsyYA_83XwukiC8sxpVQzTT7L9q6EF

# Email Configuration
ADMIN_EMAIL=<EMAIL>
EMAIL_FROM=<EMAIL>
NEXT_PUBLIC_APP_URL=http://localhost:3000
EMAIL_TESTING=true

# Stripe API Keys
STRIPE_DEVELOPMENT_SECRET_KEY=sk_test_51OvCnXJXXXXXXXXXXXXXXXXXX
NEXT_PUBLIC_STRIPE_DEVELOPMENT_PUBLISHABLE_KEY=pk_test_51OvCnXJXXXXXXXXXXXXXXXXXX
STRIPE_DEVELOPMENT_WEBHOOK_SECRET=whsec_XXXXXXXXXXXXXXXXXXXXXXXX


# Google OAuth credentials
GOOGLE_CLIENT_ID=************-m8crhd4fsee9g44f73kht29ms9nailch.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# NextAuth configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=12Pj+kQlA+CE+tjWw5AU6csRAymyWZugpEKH4LBE1uo=

# Replace these with your actual Stripe test keys from the Stripe Dashboard
# You can find these at https://dashboard.stripe.com/test/apikeys

# Google Maps API Key
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=AIzaSyD4QGWFoVWBeDgGpd95DsAwDjeRTAQFyuo





RESEND_API_KEY=re_BbuTsyYA_83XwukiC8sxpVQzTT7L9q6EF
NEXT_PUBLIC_APP_URL=http://localhost:3000
AUTH_SECRET=12Pj+kQlA+CE+tjWw5AU6csRAymyWZugpEKH4LBE1uo=
NEXTAUTH_SECRET=12Pj+kQlA+CE+tjWw5AU6csRAymyWZugpEKH4LBE1uo=
NEXTAUTH_URL=http://localhost:3000
DATABASE_URL=postgresql://postgres:1234@localhost:5432/events?schema=public
DIRECT_URL=postgresql://postgres:1234@localhost:5432/events?schema=public
JWT_SECRET=kueygeiugetuergiegkergeyewvjhbriu
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=AIzaSyD4QGWFoVWBeDgGpd95DsAwDjeRTAQFyuo
NEXT_PUBLIC_EXCHANGE_RATE_API_KEY=ad2e5f6056e367228a90c741
LLAMA_API_KEY=LA-fe435753a54249f48dea66e67f3c2030fd95a32c0bb84f778908e369b83706c2
OPENAI_API_KEY=***********************************************************************************************

# Google OAuth credentials
GOOGLE_CLIENT_ID=************-m8crhd4fsee9g44f73kht29ms9nailch.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# GitHub OAuth credentials
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

# Enable email sending in development
EMAIL_TESTING=true

# Default FROM email address
EMAIL_FROM=<EMAIL>

# Default superadmin account credentials
DEFAULT_SUPERADMIN_EMAIL=<EMAIL>
DEFAULT_SUPERADMIN_PASSWORD=SuperAdmin@123456
DEFAULT_SUPERADMIN_NAME=System Super Administrator

# Default admin account credentials
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=Admin@123456
DEFAULT_ADMIN_NAME=System Administrator

# Trust host for Next.js
TRUST_HOST=true

SHADOW_DATABASE_URL="postgresql://postgres:1234@localhost:5432/events?schema=public_shadow"

# Stripe API keys
STRIPE_SECRET_KEY=sk_test_51OvCnXJJCXXXXXXXXXXXXXXX
STRIPE_PUBLISHABLE_KEY=pk_test_51OvCnXJJCXXXXXXXXXXXXXX
STRIPE_WEBHOOK_SECRET=whsec_XXXXXXXXXXXXXXXXXXXXXXXX11
