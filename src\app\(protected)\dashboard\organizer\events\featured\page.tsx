'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Star, ArrowRight, Calendar, Users, DollarSign } from 'lucide-react';
import Link from 'next/link';

export default function FeaturedEventsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Featured Events</h1>
        <p className="text-gray-600">Promote your events to reach a wider audience and increase ticket sales.</p>
      </div>

      {/* Feature Your Events Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-500" />
              Feature Your Events
            </CardTitle>
            <CardDescription>
              Boost your event visibility and reach more potential attendees
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Increased visibility in search results</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Featured placement on homepage</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Priority in category listings</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Social media promotion</span>
              </div>
            </div>
            <Button asChild className="w-full">
              <Link href="/dashboard/featured">
                Promote Your Events
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Promotion Benefits</CardTitle>
            <CardDescription>
              See how featuring your events can impact your success
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="flex items-center justify-center mb-2">
                  <Users className="h-8 w-8 text-blue-500" />
                </div>
                <div className="text-2xl font-bold text-blue-600">3x</div>
                <div className="text-sm text-gray-600">More Views</div>
              </div>
              <div>
                <div className="flex items-center justify-center mb-2">
                  <Calendar className="h-8 w-8 text-green-500" />
                </div>
                <div className="text-2xl font-bold text-green-600">2.5x</div>
                <div className="text-sm text-gray-600">Ticket Sales</div>
              </div>
              <div>
                <div className="flex items-center justify-center mb-2">
                  <DollarSign className="h-8 w-8 text-purple-500" />
                </div>
                <div className="text-2xl font-bold text-purple-600">40%</div>
                <div className="text-sm text-gray-600">More Revenue</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Current Featured Events */}
      <Card>
        <CardHeader>
          <CardTitle>Your Featured Events</CardTitle>
          <CardDescription>
            Events you have currently promoted
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Star className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Featured Events</h3>
            <p className="text-gray-500 mb-4">
              You haven't featured any events yet. Start promoting your events to reach more attendees.
            </p>
            <Button asChild>
              <Link href="/dashboard/featured">
                Feature an Event
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
