'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CalendarDays, LayoutGrid, Plus, Search } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';

export default function SeatingManagementPage() {
  const router = useRouter();
  const [events, setEvents] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState('all');

  useEffect(() => {
    async function fetchEvents() {
      try {
        setIsLoading(true);
        const response = await fetch('/api/events/my-events');
        if (response.ok) {
          const data = await response.json();
          setEvents(data);
        } else {
          console.error('Failed to fetch events');
        }
      } catch (error) {
        console.error('Error fetching events:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchEvents();
  }, []);

  // Filter events based on search query and filter
  const filteredEvents = events.filter(event => {
    const matchesSearch = event.title.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (filter === 'all') return matchesSearch;
    if (filter === 'with-seating') return matchesSearch && event.hasStadiumSeating;
    if (filter === 'without-seating') return matchesSearch && !event.hasStadiumSeating;
    
    return matchesSearch;
  });

  const handleManageSeating = (eventId: string) => {
    router.push(`/dashboard/organizer/events/${eventId}/seating`);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Seating Management</h1>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Manage Event Seating</CardTitle>
          <CardDescription>
            Create and manage stadium seating layouts for your events. Define sections, rows, and seats to allow attendees to select specific seats during checkout.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="Search events..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Select value={filter} onValueChange={setFilter}>
              <SelectTrigger className="w-full md:w-[200px]">
                <SelectValue placeholder="Filter events" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Events</SelectItem>
                <SelectItem value="with-seating">With Seating</SelectItem>
                <SelectItem value="without-seating">Without Seating</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : filteredEvents.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredEvents.map((event) => (
                <Card key={event.id} className="overflow-hidden border border-gray-200 hover:border-primary/50 transition-all hover:shadow-md">
                  <div className="h-40 bg-gray-200 relative">
                    {event.imagePath ? (
                      <img
                        src={event.imagePath}
                        alt={event.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-r from-blue-400 to-purple-500">
                        <CalendarDays className="h-12 w-12 text-white" />
                      </div>
                    )}
                    {event.hasStadiumSeating && (
                      <Badge className="absolute top-2 right-2 bg-primary">
                        Seating Enabled
                      </Badge>
                    )}
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-lg mb-1 truncate">{event.title}</h3>
                    <p className="text-sm text-gray-500 mb-3">
                      {new Date(event.startDate).toLocaleDateString()} • {event.venue}
                    </p>
                    <Button
                      onClick={() => handleManageSeating(event.id)}
                      className="w-full"
                      variant={event.hasStadiumSeating ? "default" : "outline"}
                    >
                      <LayoutGrid className="mr-2 h-4 w-4" />
                      {event.hasStadiumSeating ? "Manage Seating" : "Set Up Seating"}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <LayoutGrid className="mx-auto h-12 w-12 text-gray-400 mb-2" />
              <h3 className="text-lg font-medium">No events found</h3>
              <p className="text-gray-500 mb-4">
                {searchQuery
                  ? "No events match your search criteria."
                  : "You haven't created any events yet."}
              </p>
              <Button onClick={() => router.push('/dashboard/organizer/events/createevent')}>
                <Plus className="mr-2 h-4 w-4" />
                Create New Event
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Seating Management Guide</CardTitle>
          <CardDescription>
            Learn how to create and manage seating layouts for your events
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start gap-4">
              <div className="bg-primary/10 p-2 rounded-full">
                <span className="font-bold text-primary">1</span>
              </div>
              <div>
                <h3 className="font-medium">Create Sections</h3>
                <p className="text-gray-600">
                  Define different sections in your venue (e.g., Floor, Balcony, VIP). Each section can have different pricing and seating arrangements.
                </p>
              </div>
            </div>
            
            <Separator />
            
            <div className="flex items-start gap-4">
              <div className="bg-primary/10 p-2 rounded-full">
                <span className="font-bold text-primary">2</span>
              </div>
              <div>
                <h3 className="font-medium">Configure Rows and Seats</h3>
                <p className="text-gray-600">
                  For each section, specify the number of rows and seats per row. Rows are automatically labeled (A, B, C...) and seats are numbered.
                </p>
              </div>
            </div>
            
            <Separator />
            
            <div className="flex items-start gap-4">
              <div className="bg-primary/10 p-2 rounded-full">
                <span className="font-bold text-primary">3</span>
              </div>
              <div>
                <h3 className="font-medium">Set Pricing Categories</h3>
                <p className="text-gray-600">
                  Assign pricing categories (Regular, VIP, VVIP) to each section. This determines the price of seats in that section.
                </p>
              </div>
            </div>
            
            <Separator />
            
            <div className="flex items-start gap-4">
              <div className="bg-primary/10 p-2 rounded-full">
                <span className="font-bold text-primary">4</span>
              </div>
              <div>
                <h3 className="font-medium">Preview and Save</h3>
                <p className="text-gray-600">
                  Preview your seating layout to ensure it matches your venue. Once saved, attendees will be able to select specific seats during checkout.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
