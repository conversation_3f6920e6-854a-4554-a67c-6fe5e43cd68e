import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@/lib/auth';
import { db } from '@/lib/prisma';

// Type definitions
interface ApiError {
  error: string;
  details?: string;
}

// Helper function to check admin permissions
async function checkAdminPermissions(currentUserData: any): Promise<boolean> {
  return currentUserData?.role === 'ADMIN' || 
         currentUserData?.role === 'SUPERADMIN' || 
         currentUserData?.role === 'DEVELOPER';
}

// Helper function to log activity
async function logActivity(
  adminId: string,
  action: string,
  resource: string,
  resourceId?: string,
  details?: any,
  success: boolean = true,
  errorMessage?: string,
  ipAddress?: string,
  userAgent?: string
) {
  try {
    await db.activityLog.create({
      data: {
        adminId,
        action,
        resource,
        resourceId,
        details,
        success,
        errorMessage,
        ipAddress,
        userAgent,
      },
    });
  } catch (error) {
    console.error('Failed to log activity:', error);
  }
}

// GET: Fetch activity logs with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();
    const ipAddress = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    if (!user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' } as ApiError,
        { status: 401 }
      );
    }

    const hasPermission = await checkAdminPermissions(user);
    if (!hasPermission) {
      await logActivity(
        user.id,
        'UNAUTHORIZED_VIEW_ACTIVITY_LOGS_ATTEMPT',
        'activity_logs',
        undefined,
        { endpoint: '/api/admin/activity-logs' },
        false,
        'Insufficient permissions',
        ipAddress,
        userAgent
      );
      return NextResponse.json(
        { error: 'Insufficient permissions' } as ApiError,
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const action = searchParams.get('action') || '';
    const resource = searchParams.get('resource') || '';
    const adminId = searchParams.get('adminId') || '';
    const success = searchParams.get('success');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const sortBy = searchParams.get('sortBy') || 'timestamp';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (action) {
      where.action = { contains: action, mode: 'insensitive' };
    }
    
    if (resource) {
      where.resource = { contains: resource, mode: 'insensitive' };
    }
    
    if (adminId) {
      where.adminId = adminId;
    }
    
    if (success !== null && success !== undefined && success !== '') {
      where.success = success === 'true';
    }
    
    if (startDate || endDate) {
      where.timestamp = {};
      if (startDate) {
        where.timestamp.gte = new Date(startDate);
      }
      if (endDate) {
        where.timestamp.lte = new Date(endDate);
      }
    }

    // Build orderBy clause
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    const [logs, totalCount] = await Promise.all([
      db.activityLog.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        select: {
          id: true,
          userId: true,
          adminId: true,
          action: true,
          resource: true,
          resourceId: true,
          details: true,
          ipAddress: true,
          userAgent: true,
          timestamp: true,
          success: true,
          errorMessage: true,
        },
      }),
      db.activityLog.count({ where }),
    ]);

    // Get admin user details for the logs
    const adminIds = [...new Set(logs.map(log => log.adminId).filter(Boolean))];
    const adminUsers = await db.user.findMany({
      where: { id: { in: adminIds } },
      select: { id: true, name: true, email: true, role: true },
    });

    const adminMap = new Map(adminUsers.map(admin => [admin.id, admin]));

    // Transform logs with admin details
    const transformedLogs = logs.map(log => ({
      ...log,
      admin: log.adminId ? adminMap.get(log.adminId) : null,
      timestamp: log.timestamp.toISOString(),
    }));

    const totalPages = Math.ceil(totalCount / limit);

    await logActivity(
      user.id,
      'VIEW_ACTIVITY_LOGS',
      'activity_logs',
      undefined,
      { 
        page, 
        limit, 
        filters: { action, resource, adminId, success, startDate, endDate },
        totalCount 
      },
      true,
      undefined,
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      logs: transformedLogs,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      summary: {
        totalLogs: totalCount,
        successfulActions: logs.filter(log => log.success).length,
        failedActions: logs.filter(log => !log.success).length,
        uniqueAdmins: adminIds.length,
      },
    });

  } catch (error) {
    console.error('Error fetching activity logs:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      } as ApiError,
      { status: 500 }
    );
  }
}

// GET: Get activity log statistics
export async function POST(request: NextRequest) {
  try {
    const user = await currentUser();
    const ipAddress = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    if (!user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' } as ApiError,
        { status: 401 }
      );
    }

    const hasPermission = await checkAdminPermissions(user);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Insufficient permissions' } as ApiError,
        { status: 403 }
      );
    }

    const body = await request.json();
    const { startDate, endDate } = body;

    const dateFilter: any = {};
    if (startDate) dateFilter.gte = new Date(startDate);
    if (endDate) dateFilter.lte = new Date(endDate);

    const where = startDate || endDate ? { timestamp: dateFilter } : {};

    // Get statistics
    const [
      totalLogs,
      successfulLogs,
      failedLogs,
      actionStats,
      resourceStats,
      adminStats,
      recentFailures,
    ] = await Promise.all([
      db.activityLog.count({ where }),
      db.activityLog.count({ where: { ...where, success: true } }),
      db.activityLog.count({ where: { ...where, success: false } }),
      
      // Action statistics
      db.activityLog.groupBy({
        by: ['action'],
        where,
        _count: { action: true },
        orderBy: { _count: { action: 'desc' } },
        take: 10,
      }),
      
      // Resource statistics
      db.activityLog.groupBy({
        by: ['resource'],
        where,
        _count: { resource: true },
        orderBy: { _count: { resource: 'desc' } },
        take: 10,
      }),
      
      // Admin statistics
      db.activityLog.groupBy({
        by: ['adminId'],
        where,
        _count: { adminId: true },
        orderBy: { _count: { adminId: 'desc' } },
        take: 10,
      }),
      
      // Recent failures
      db.activityLog.findMany({
        where: { ...where, success: false },
        orderBy: { timestamp: 'desc' },
        take: 10,
        select: {
          id: true,
          action: true,
          resource: true,
          errorMessage: true,
          timestamp: true,
          adminId: true,
        },
      }),
    ]);

    // Get admin details for statistics
    const adminIds = adminStats.map(stat => stat.adminId).filter(Boolean);
    const adminUsers = await db.user.findMany({
      where: { id: { in: adminIds } },
      select: { id: true, name: true, email: true },
    });
    const adminMap = new Map(adminUsers.map(admin => [admin.id, admin]));

    await logActivity(
      user.id,
      'VIEW_ACTIVITY_STATISTICS',
      'activity_logs',
      undefined,
      { startDate, endDate },
      true,
      undefined,
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      summary: {
        totalLogs,
        successfulLogs,
        failedLogs,
        successRate: totalLogs > 0 ? (successfulLogs / totalLogs) * 100 : 0,
      },
      topActions: actionStats.map(stat => ({
        action: stat.action,
        count: stat._count.action,
      })),
      topResources: resourceStats.map(stat => ({
        resource: stat.resource,
        count: stat._count.resource,
      })),
      topAdmins: adminStats.map(stat => ({
        adminId: stat.adminId,
        admin: stat.adminId ? adminMap.get(stat.adminId) : null,
        count: stat._count.adminId,
      })),
      recentFailures: recentFailures.map(failure => ({
        ...failure,
        timestamp: failure.timestamp.toISOString(),
        admin: failure.adminId ? adminMap.get(failure.adminId) : null,
      })),
    });

  } catch (error) {
    console.error('Error fetching activity statistics:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      } as ApiError,
      { status: 500 }
    );
  }
}
