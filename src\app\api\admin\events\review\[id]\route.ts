import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await currentUser();

    // Check if user is authenticated and has admin role
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const eventId = resolvedParams.id;
    const { action, comments } = await request.json();

    // Validate action
    if (action !== 'approve' && action !== 'reject') {
      return NextResponse.json(
        { error: 'Invalid action. Must be "approve" or "reject"' },
        { status: 400 }
      );
    }

    // If rejecting, comments are required
    if (action === 'reject' && !comments) {
      return NextResponse.json(
        { error: 'Comments are required when rejecting an event' },
        { status: 400 }
      );
    }

    // Get the event
    const event = await db.event.findUnique({
      where: { id: eventId },
      select: {
        id: true,
        title: true,
        userId: true,
        status: true,
      },
    });

    // Check if event exists
    if (!event) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }

    // Check if event is under review
    if (event.status !== 'UnderReview') {
      return NextResponse.json(
        { error: 'Event is not under review' },
        { status: 400 }
      );
    }

    // Update event status based on action
    const newStatus = action === 'approve' ? 'Approved' : 'Rejected';

    // First, get the current event to access its metadata
    const currentEvent = await db.event.findUnique({
      where: { id: eventId },
      select: { metadata: true }
    });

    // Prepare the update data
    let updateData: any = { status: newStatus };

    // Handle metadata updates properly
    if (action === 'reject') {
      // Merge with existing metadata instead of overwriting
      updateData.metadata = {
        ...(typeof currentEvent?.metadata === 'object' && currentEvent?.metadata !== null ? currentEvent.metadata as any : {}),
        rejectionReason: comments
      };
    }

    const updatedEvent = await db.event.update({
      where: { id: eventId },
      data: updateData,
    });

    // Skip creating event review record for now until the model is properly set up
    // We'll add this functionality back once the Prisma schema is updated and generated

    // For now, just log that we would create a review
    console.log('Would create/update event review:', {
      eventId,
      reviewerId: user.id,
      status: action === 'approve' ? 'APPROVED' : 'REJECTED',
      comments: comments,
      reviewedAt: new Date(),
    });

    // Create notification for the event owner
    await db.notification.create({
      data: {
        userId: event.userId,
        message: action === 'approve'
          ? `Your event "${event.title}" has been approved. You can now publish it from your dashboard.`
          : `Your event "${event.title}" has been rejected. Reason: ${comments}`,
        type: action === 'approve' ? 'EVENT_APPROVED' : 'EVENT_REJECTED',
        isRead: false
      }
    });

    return NextResponse.json({
      success: true,
      message: `Event ${action === 'approve' ? 'approved' : 'rejected'} successfully`,
      event: {
        id: updatedEvent.id,
        status: updatedEvent.status
      }
    });
  } catch (error) {
    console.error('Error reviewing event:', error);
    return NextResponse.json(
      { error: 'Failed to review event' },
      { status: 500 }
    );
  }
}
