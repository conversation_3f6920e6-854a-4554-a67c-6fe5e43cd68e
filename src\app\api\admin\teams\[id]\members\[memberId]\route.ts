import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

// GET /api/admin/teams/[id]/members/[memberId] - Get a specific team member (admin only)
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string; memberId: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const params = await context.params;
    const teamId = params.id;
    const memberId = params.memberId;

    // Get the team
    const team = await db.team.findUnique({
      where: { id: teamId },
    });

    // Check if team exists
    if (!team) {
      return NextResponse.json(
        { error: 'Team not found' },
        { status: 404 }
      );
    }

    // Get the team member
    const member = await db.teamMember.findUnique({
      where: {
        id: memberId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    // Check if member exists and belongs to the team
    if (!member || member.teamId !== teamId) {
      return NextResponse.json(
        { error: 'Team member not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      member,
    });
  } catch (error) {
    console.error('Error fetching team member:', error);
    return NextResponse.json(
      { error: 'Failed to fetch team member' },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/teams/[id]/members/[memberId] - Update a team member's role (admin only)
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string; memberId: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const params = await context.params;
    const teamId = (await params).id;
    const memberId = (await params).memberId;

    // Get the team
    const team = await db.team.findUnique({
      where: { id: teamId },
    });

    // Check if team exists
    if (!team) {
      return NextResponse.json(
        { error: 'Team not found' },
        { status: 404 }
      );
    }

    // Get the team member
    const member = await db.teamMember.findUnique({
      where: {
        id: memberId,
      },
      include: {
        user: true,
      },
    });

    // Check if member exists and belongs to the team
    if (!member || member.teamId !== teamId) {
      return NextResponse.json(
        { error: 'Team member not found' },
        { status: 404 }
      );
    }

    // Prevent changing the role of the team owner
    if (member.user.id === team.ownerId) {
      return NextResponse.json(
        { error: 'Cannot change the role of the team owner' },
        { status: 400 }
      );
    }

    // Get request body
    const body = await request.json();
    const { role, permissions } = body;

    // Validate role
    if (role && !['ORGANIZER_ADMIN', 'ORGANIZER_MANAGER', 'ORGANIZER_EDITOR', 'ORGANIZER_ANALYST', 'ORGANIZER_SUPPORT'].includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role' },
        { status: 400 }
      );
    }

    // Update the team member
    const updatedMember = await db.teamMember.update({
      where: {
        id: memberId,
      },
      data: {
        role: role || undefined,
        permissions: permissions !== undefined ? permissions : undefined,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    // Create a notification for the user
    await db.notification.create({
      data: {
        userId: member.userId,
        message: `Your role in the team "${team.name}" has been updated to ${role} by an administrator`,
        type: 'TEAM_ROLE_UPDATED',
        isRead: false,
      },
    });

    return NextResponse.json({
      success: true,
      member: updatedMember,
    });
  } catch (error) {
    console.error('Error updating team member:', error);
    return NextResponse.json(
      { error: 'Failed to update team member' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/teams/[id]/members/[memberId] - Remove a member from a team (admin only)
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string; memberId: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const params = await context.params;
    const teamId = (await params).id;
    const memberId = (await params).memberId;

    // Get the team
    const team = await db.team.findUnique({
      where: { id: teamId },
    });

    // Check if team exists
    if (!team) {
      return NextResponse.json(
        { error: 'Team not found' },
        { status: 404 }
      );
    }

    // Get the team member
    const member = await db.teamMember.findUnique({
      where: {
        id: memberId,
      },
      include: {
        user: true,
      },
    });

    // Check if member exists and belongs to the team
    if (!member || member.teamId !== teamId) {
      return NextResponse.json(
        { error: 'Team member not found' },
        { status: 404 }
      );
    }

    // Prevent removing the team owner
    if (member.user.id === team.ownerId) {
      return NextResponse.json(
        { error: 'Cannot remove the team owner' },
        { status: 400 }
      );
    }

    // Remove the team member
    await db.teamMember.delete({
      where: {
        id: memberId,
      },
    });

    // Create a notification for the user
    await db.notification.create({
      data: {
        userId: member.userId,
        message: `You have been removed from the team "${team.name}" by an administrator`,
        type: 'TEAM_REMOVED',
        isRead: false,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Team member removed successfully',
    });
  } catch (error) {
    console.error('Error removing team member:', error);
    return NextResponse.json(
      { error: 'Failed to remove team member' },
      { status: 500 }
    );
  }
}
