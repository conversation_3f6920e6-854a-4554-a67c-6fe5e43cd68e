import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { sendTeamInvitationEmail } from '@/lib/email';

// GET /api/admin/teams/[id]/members - Get all members of a team (admin only)
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
    const resolvedParams = await context.params;

  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const params = await context.params;
    const teamId = resolvedParams.id;

    // Get the team
    const team = await db.team.findUnique({
      where: { id: teamId },
    });

    // Check if team exists
    if (!team) {
      return NextResponse.json(
        { error: 'Team not found' },
        { status: 404 }
      );
    }

    // Get team members
    const members = await db.teamMember.findMany({
      where: { teamId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    return NextResponse.json({
      members,
    });
  } catch (error) {
    console.error('Error fetching team members:', error);
    return NextResponse.json(
      { error: 'Failed to fetch team members' },
      { status: 500 }
    );
  }
}

// POST /api/admin/teams/[id]/members - Add a member to a team (admin only)
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const params = await context.params;
    const teamId = (await params).id;

    // Get the team
    const team = await db.team.findUnique({
      where: { id: teamId },
    });

    // Check if team exists
    if (!team) {
      return NextResponse.json(
        { error: 'Team not found' },
        { status: 404 }
      );
    }

    // Get request body
    const body = await request.json();
    const { email, role } = body;

    // Validate required fields
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    if (!role || !['ORGANIZER_ADMIN', 'ORGANIZER_MANAGER', 'ORGANIZER_EDITOR', 'ORGANIZER_ANALYST', 'ORGANIZER_SUPPORT'].includes(role)) {
      return NextResponse.json(
        { error: 'Valid role is required' },
        { status: 400 }
      );
    }

    // Check if the user exists
    const targetUser = await db.user.findUnique({
      where: { email }
    });

    if (!targetUser) {
      // If user doesn't exist, create an invitation
      const token = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7); // Expires in 7 days

      const invitation = await db.teamInvitation.create({
        data: {
          teamId,
          email,
          role,
          token,
          expiresAt,
          invitedById: user.id,
          status: 'PENDING'
        },
        include: {
          invitedBy: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          }
        }
      });

      // Send invitation email
      try {
        await sendTeamInvitationEmail(
          email,
          team.name,
          user.name || user.email || 'Admin',
          role,
          token
        );
      } catch (emailError) {
        console.error('Failed to send invitation email:', emailError);
        // Continue with the process even if email fails
      }

      return NextResponse.json({
        success: true,
        message: 'Invitation sent',
        invitation: {
          id: invitation.id,
          email: invitation.email,
          role: invitation.role,
          expiresAt: invitation.expiresAt,
          token: invitation.token,
          invitedBy: invitation.invitedBy
        }
      }, { status: 201 });
    }

    // Check if the user is already a member of the team
    const existingMember = await db.teamMember.findUnique({
      where: {
        teamId_userId: {
          teamId,
          userId: targetUser.id
        }
      }
    });

    if (existingMember) {
      return NextResponse.json(
        { error: 'User is already a member of this team' },
        { status: 400 }
      );
    }

    // Add the user to the team
    const member = await db.teamMember.create({
      data: {
        teamId,
        userId: targetUser.id,
        role
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        }
      }
    });

    // Create a notification for the user
    await db.notification.create({
      data: {
        userId: targetUser.id,
        message: `You have been added to the team "${team.name}" by an administrator`,
        type: 'TEAM_ADDED',
        isRead: false
      }
    });

    return NextResponse.json({
      success: true,
      member
    }, { status: 201 });
  } catch (error) {
    console.error('Error adding team member:', error);
    return NextResponse.json(
      { error: 'Failed to add team member' },
      { status: 500 }
    );
  }
}
