import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

// GET /api/admin/teams/[id] - Get team details (admin only)
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
    const resolvedParams = await context.params;

  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const params = await context.params;
    const teamId = resolvedParams.id;

    // Get the team with members
    const team = await db.team.findUnique({
      where: { id: teamId },
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        members: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
        _count: {
          select: {
            members: true,
            events: true,
            invitations: true,
          },
        },
      },
    });

    // Check if team exists
    if (!team) {
      return NextResponse.json(
        { error: 'Team not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      team,
    });
  } catch (error) {
    console.error('Error fetching team details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch team details' },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/teams/[id] - Update team details (admin only)
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await context.params;
    const teamId = resolvedParams.id;

    // Get request body
    const body = await request.json();
    const { name, description, ownerId } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: 'Team name is required' },
        { status: 400 }
      );
    }

    // Check if team exists
    const existingTeam = await db.team.findUnique({
      where: { id: teamId },
    });

    if (!existingTeam) {
      return NextResponse.json(
        { error: 'Team not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {
      name,
      description,
    };

    // If owner ID is provided, update the owner
    if (ownerId) {
      // Check if the new owner exists
      const newOwner = await db.user.findUnique({
        where: { id: ownerId },
      });

      if (!newOwner) {
        return NextResponse.json(
          { error: 'New owner not found' },
          { status: 400 }
        );
      }

      updateData.ownerId = ownerId;

      // Ensure the new owner is a member of the team with ORGANIZER_ADMIN role
      const ownerMember = await db.teamMember.findUnique({
        where: {
          teamId_userId: {
            teamId,
            userId: ownerId,
          },
        },
      });

      if (!ownerMember) {
        // Add the new owner as a team member
        await db.teamMember.create({
          data: {
            teamId,
            userId: ownerId,
            role: 'ORGANIZER_ADMIN',
          },
        });
      } else if (ownerMember.role !== 'ORGANIZER_ADMIN') {
        // Update the role to ORGANIZER_ADMIN
        await db.teamMember.update({
          where: {
            id: ownerMember.id,
          },
          data: {
            role: 'ORGANIZER_ADMIN',
          },
        });
      }
    }

    // Update the team
    const updatedTeam = await db.team.update({
      where: { id: teamId },
      data: updateData,
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        _count: {
          select: {
            members: true,
            events: true,
            invitations: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      team: updatedTeam,
    });
  } catch (error) {
    console.error('Error updating team:', error);
    return NextResponse.json(
      { error: 'Failed to update team' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/teams/[id] - Delete a team (admin only)
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await context.params;
    const teamId = resolvedParams.id;

    // Check if team exists
    const team = await db.team.findUnique({
      where: { id: teamId },
      include: {
        events: true,
      },
    });

    if (!team) {
      return NextResponse.json(
        { error: 'Team not found' },
        { status: 404 }
      );
    }

    // Check if the team has events
    if (team.events.length > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete team with events',
          message: 'This team has events associated with it. Please reassign or delete the events before deleting the team.'
        },
        { status: 400 }
      );
    }

    // Delete team members
    await db.teamMember.deleteMany({
      where: { teamId },
    });

    // Delete team invitations
    await db.teamInvitation.deleteMany({
      where: { teamId },
    });

    // Delete the team
    await db.team.delete({
      where: { id: teamId },
    });

    return NextResponse.json({
      success: true,
      message: 'Team deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting team:', error);
    return NextResponse.json(
      { error: 'Failed to delete team' },
      { status: 500 }
    );
  }
}
