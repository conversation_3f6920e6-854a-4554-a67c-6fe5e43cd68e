import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

// GET /api/admin/teams - Get all teams (admin only)
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search') || '';
    const sort = searchParams.get('sort') || 'createdAt';
    const order = searchParams.get('order') || 'desc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build the where clause for search
    const where = search
      ? {
          OR: [
            { name: { contains: search, mode: 'insensitive' as const } },
            { owner: { name: { contains: search, mode: 'insensitive' as const } } },
            { owner: { email: { contains: search, mode: 'insensitive' as const } } },
          ],
        }
      : {};

    // Build the orderBy clause
    let orderBy: any = {};
    if (sort === 'name') {
      orderBy.name = order;
    } else if (sort === 'owner') {
      orderBy.owner = { name: order };
    } else {
      orderBy.createdAt = order;
    }

    // Get teams with pagination
    const teams = await db.team.findMany({
      where,
      orderBy,
      skip,
      take: limit,
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        _count: {
          select: {
            members: true,
            events: true,
            invitations: true,
          },
        },
      },
    });

    // Get total count for pagination
    const totalCount = await db.team.count({ where });

    return NextResponse.json({
      teams,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching teams:', error);
    return NextResponse.json(
      { error: 'Failed to fetch teams' },
      { status: 500 }
    );
  }
}

// POST /api/admin/teams - Create a new team (admin only)
export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await request.json();
    const { name, description, ownerEmail } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: 'Team name is required' },
        { status: 400 }
      );
    }

    // Determine the owner
    let ownerId = user.id; // Default to current admin user

    if (ownerEmail) {
      // If owner email is provided, find or create that user
      const owner = await db.user.findUnique({
        where: { email: ownerEmail },
      });

      if (owner) {
        ownerId = owner.id;
      } else {
        return NextResponse.json(
          { error: 'User with provided email not found' },
          { status: 400 }
        );
      }
    }

    // Create the team
    const team = await db.team.create({
      data: {
        name,
        description,
        ownerId,
        // Add the owner as a team member with ORGANIZER_ADMIN role
        members: {
          create: {
            userId: ownerId,
            role: 'ORGANIZER_ADMIN',
          },
        },
      },
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        members: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
        _count: {
          select: {
            members: true,
            events: true,
            invitations: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      team,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating team:', error);
    return NextResponse.json(
      { error: 'Failed to create team' },
      { status: 500 }
    );
  }
}
