import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@/lib/auth';
import { db } from '@/lib/prisma';
import { z } from 'zod';
import { sendUserInvitationEmail } from '@/lib/email-service';

// Validation schema
const inviteUserSchema = z.object({
  email: z.string().email('Valid email is required'),
  role: z.enum(['ADMIN', 'USER', 'ORGANIZER', 'VENDOR', 'SUPERADMIN', 'DEVELOPER', 'PARTNER']),
  name: z.string().min(1, 'Name is required').optional(),
});

// Type definitions
interface ApiError {
  error: string;
  details?: string;
}

// Helper function to check admin permissions
async function checkAdminPermissions(currentUserData: any): Promise<boolean> {
  return currentUserData?.role === 'ADMIN' || 
         currentUserData?.role === 'SUPERADMIN' || 
         currentUserData?.role === 'DEVELOPER';
}

// Helper function to log activity
async function logActivity(
  adminId: string,
  action: string,
  resource: string,
  resourceId?: string,
  details?: any,
  success: boolean = true,
  errorMessage?: string,
  ipAddress?: string,
  userAgent?: string
) {
  try {
    await db.activityLog.create({
      data: {
        adminId,
        action,
        resource,
        resourceId,
        details,
        success,
        errorMessage,
        ipAddress,
        userAgent,
      },
    });
  } catch (error) {
    console.error('Failed to log activity:', error);
  }
}

// POST: Send user invitation
export async function POST(request: NextRequest) {
  try {
    const user = await currentUser();
    const ipAddress = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    if (!user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' } as ApiError,
        { status: 401 }
      );
    }

    const hasPermission = await checkAdminPermissions(user);
    if (!hasPermission) {
      await logActivity(
        user.id,
        'UNAUTHORIZED_INVITE_USER_ATTEMPT',
        'user_invitations',
        undefined,
        { endpoint: '/api/admin/users/invite' },
        false,
        'Insufficient permissions',
        ipAddress,
        userAgent
      );
      return NextResponse.json(
        { error: 'Insufficient permissions' } as ApiError,
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = inviteUserSchema.parse(body);

    // Check if user already exists
    const existingUser = await db.user.findUnique({
      where: { email: validatedData.email },
    });

    if (existingUser) {
      await logActivity(
        user.id,
        'INVITE_USER_FAILED',
        'user_invitations',
        undefined,
        { email: validatedData.email },
        false,
        'User already exists',
        ipAddress,
        userAgent
      );
      return NextResponse.json(
        { error: 'User with this email already exists' } as ApiError,
        { status: 409 }
      );
    }

    // Check if there's already a pending invitation
    const existingInvitation = await db.userInvitation.findFirst({
      where: {
        email: validatedData.email,
        status: 'PENDING',
        expiresAt: { gt: new Date() },
      },
    });

    if (existingInvitation) {
      await logActivity(
        user.id,
        'INVITE_USER_FAILED',
        'user_invitations',
        existingInvitation.id,
        { email: validatedData.email },
        false,
        'Pending invitation already exists',
        ipAddress,
        userAgent
      );
      return NextResponse.json(
        { error: 'A pending invitation already exists for this email' } as ApiError,
        { status: 409 }
      );
    }

    // Generate invitation token and password
    const token = Math.random().toString(36).slice(-32) + Date.now().toString(36);
    const password = Math.random().toString(36).slice(-12) + 'A1!';
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

    // Create invitation
    const invitation = await db.userInvitation.create({
      data: {
        email: validatedData.email,
        role: validatedData.role,
        invitedBy: user.id,
        token,
        password,
        expiresAt,
      },
    });

    // Send invitation email
    try {
      const emailResult = await sendUserInvitationEmail(
        validatedData.email,
        validatedData.name || validatedData.email,
        password,
        validatedData.role,
        token
      );

      if (!emailResult.success) {
        // Delete the invitation if email failed
        await db.userInvitation.delete({
          where: { id: invitation.id },
        });

        await logActivity(
          user.id,
          'INVITE_USER_FAILED',
          'user_invitations',
          invitation.id,
          { email: validatedData.email, emailError: emailResult.error },
          false,
          'Failed to send invitation email',
          ipAddress,
          userAgent
        );

        return NextResponse.json(
          { 
            error: 'Failed to send invitation email',
            details: emailResult.error 
          } as ApiError,
          { status: 500 }
        );
      }

      await logActivity(
        user.id,
        'INVITE_USER',
        'user_invitations',
        invitation.id,
        { 
          email: validatedData.email,
          role: validatedData.role,
          expiresAt: expiresAt.toISOString()
        },
        true,
        undefined,
        ipAddress,
        userAgent
      );

      return NextResponse.json({
        message: 'User invitation sent successfully',
        invitation: {
          id: invitation.id,
          email: invitation.email,
          role: invitation.role,
          expiresAt: invitation.expiresAt,
          status: invitation.status,
        },
      }, { status: 201 });

    } catch (emailError) {
      // Delete the invitation if email failed
      await db.userInvitation.delete({
        where: { id: invitation.id },
      });

      throw emailError;
    }

  } catch (error) {
    console.error('Error sending user invitation:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation error',
          details: error.errors.map(e => e.message).join(', ')
        } as ApiError,
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      } as ApiError,
      { status: 500 }
    );
  }
}

// GET: Get pending invitations
export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();
    const ipAddress = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    if (!user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' } as ApiError,
        { status: 401 }
      );
    }

    const hasPermission = await checkAdminPermissions(user);
    if (!hasPermission) {
      await logActivity(
        user.id,
        'UNAUTHORIZED_VIEW_INVITATIONS_ATTEMPT',
        'user_invitations',
        undefined,
        { endpoint: '/api/admin/users/invite' },
        false,
        'Insufficient permissions',
        ipAddress,
        userAgent
      );
      return NextResponse.json(
        { error: 'Insufficient permissions' } as ApiError,
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status') || 'PENDING';

    const skip = (page - 1) * limit;

    const [invitations, totalCount] = await Promise.all([
      db.userInvitation.findMany({
        where: { status: status as any },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          email: true,
          role: true,
          status: true,
          expiresAt: true,
          createdAt: true,
          invitedBy: true,
        },
      }),
      db.userInvitation.count({ where: { status: status as any } }),
    ]);

    await logActivity(
      user.id,
      'VIEW_INVITATIONS',
      'user_invitations',
      undefined,
      { page, limit, status, totalCount },
      true,
      undefined,
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      invitations,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrev: page > 1,
      },
    });

  } catch (error) {
    console.error('Error fetching invitations:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      } as ApiError,
      { status: 500 }
    );
  }
}
