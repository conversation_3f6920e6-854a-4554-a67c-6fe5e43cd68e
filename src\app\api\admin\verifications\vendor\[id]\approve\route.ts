import { NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function POST(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await currentUser();
    
    if (!user || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const resolvedParams = await params;
    const { id } = resolvedParams;
    
    // Find the verification
    const verification = await db.vendorVerification.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            vendorProfile: {
              select: {
                id: true,
              },
            },
          },
        },
      },
    });
    
    if (!verification) {
      return NextResponse.json(
        { error: 'Verification not found' },
        { status: 404 }
      );
    }
    
    // Update verification status
    const updatedVerification = await db.vendorVerification.update({
      where: { id },
      data: {
        status: 'APPROVED',
        reviewedBy: user.id,
        reviewedAt: new Date(),
      },
    });
    
    // Update vendor profile verification status
    if (verification.user.vendorProfile) {
      await db.vendorProfile.update({
        where: { id: verification.user.vendorProfile.id },
        data: {
          verificationStatus: 'APPROVED',
          verifiedAt: new Date(),
        },
      });
    }
    
    // Create notification for the vendor
    await db.notification.create({
      data: {
        userId: verification.userId,
        type: 'VERIFICATION_APPROVED',
        message: 'Your vendor verification has been approved. You can now start selling products.',
      },
    });
    
    return NextResponse.json(updatedVerification);
  } catch (error) {
    console.error('Error approving vendor verification:', error);
    return NextResponse.json(
      { error: 'Failed to approve vendor verification' },
      { status: 500 }
    );
  }
}
