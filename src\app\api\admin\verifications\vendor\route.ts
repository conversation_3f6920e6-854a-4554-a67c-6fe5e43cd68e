import { NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(req: Request) {
  try {
    const user = await currentUser();
    
    if (!user || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get all vendor verifications with user info
    const verifications = await db.vendorVerification.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            vendorProfile: {
              select: {
                businessName: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
    
    // Format the response
    const formattedVerifications = verifications.map(verification => ({
      id: verification.id,
      userId: verification.userId,
      businessName: verification.user.vendorProfile?.businessName || 'Unnamed Business',
      registrationNumber: verification.registrationNumber,
      taxPayerIdNumber: verification.taxPayerIdNumber,
      phoneNumber: verification.phoneNumber,
      alternativeEmail: verification.alternativeEmail,
      physicalAddress: verification.physicalAddress,
      city: verification.city,
      province: verification.province,
      postalCode: verification.postalCode,
      idDocumentPath: verification.idDocumentPath,
      idDocumentType: verification.idDocumentType,
      businessLicensePath: verification.businessLicensePath,
      taxCertificatePath: verification.taxCertificatePath,
      status: verification.status,
      createdAt: verification.createdAt,
      updatedAt: verification.updatedAt,
      user: {
        name: verification.user.name,
        email: verification.user.email,
      },
    }));
    
    return NextResponse.json(formattedVerifications);
  } catch (error) {
    console.error('Error fetching vendor verifications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch vendor verifications' },
      { status: 500 }
    );
  }
}
