import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/withdrawals/stats
 * Get withdrawal statistics by status
 */
export async function GET(req: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can access withdrawal statistics
    if (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get URL parameters
    const { searchParams } = new URL(req.url);
    const status = searchParams.get('status');

    // Build the where clause
    const where: any = {};
    if (status) {
      where.status = status;
    }

    // Get total count
    const totalCount = await db.withdrawal.count({ where });

    // Get total amount
    const totalAmount = await db.withdrawal.aggregate({
      where,
      _sum: { amount: true },
    });

    // Get today's count
    const todayCount = await db.withdrawal.count({
      where: {
        ...where,
        requestDate: {
          gte: new Date(new Date().setHours(0, 0, 0, 0)),
        },
      },
    });

    // Get today's amount
    const todayAmount = await db.withdrawal.aggregate({
      where: {
        ...where,
        requestDate: {
          gte: new Date(new Date().setHours(0, 0, 0, 0)),
        },
      },
      _sum: { amount: true },
    });

    // Get this week's count
    const thisWeekStart = new Date();
    thisWeekStart.setDate(thisWeekStart.getDate() - thisWeekStart.getDay());
    thisWeekStart.setHours(0, 0, 0, 0);

    const thisWeekCount = await db.withdrawal.count({
      where: {
        ...where,
        requestDate: {
          gte: thisWeekStart,
        },
      },
    });

    // Get this week's amount
    const thisWeekAmount = await db.withdrawal.aggregate({
      where: {
        ...where,
        requestDate: {
          gte: thisWeekStart,
        },
      },
      _sum: { amount: true },
    });

    // Get this month's count
    const thisMonthStart = new Date();
    thisMonthStart.setDate(1);
    thisMonthStart.setHours(0, 0, 0, 0);

    const thisMonthCount = await db.withdrawal.count({
      where: {
        ...where,
        requestDate: {
          gte: thisMonthStart,
        },
      },
    });

    // Get this month's amount
    const thisMonthAmount = await db.withdrawal.aggregate({
      where: {
        ...where,
        requestDate: {
          gte: thisMonthStart,
        },
      },
      _sum: { amount: true },
    });

    return NextResponse.json({
      totalCount,
      totalAmount: totalAmount._sum.amount || 0,
      todayCount,
      todayAmount: todayAmount._sum.amount || 0,
      thisWeekCount,
      thisWeekAmount: thisWeekAmount._sum.amount || 0,
      thisMonthCount,
      thisMonthAmount: thisMonthAmount._sum.amount || 0,
    });
  } catch (error) {
    console.error('Error fetching withdrawal statistics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch withdrawal statistics' },
      { status: 500 }
    );
  }
}
