import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function POST(req: NextRequest) {
  try {
    // Extract the ID from the URL path
    const url = new URL(req.url);
    const pathParts = url.pathname.split('/');
    const alertId = pathParts[pathParts.length - 2]; // Get the ID from the URL path

    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if alert exists
    const alert = await db.apiAlert.findUnique({
      where: {
        id: alertId,
      },
    });

    if (!alert) {
      return NextResponse.json(
        { error: 'Alert not found' },
        { status: 404 }
      );
    }

    // Check if alert is already resolved
    if (alert.resolved) {
      return NextResponse.json(
        { error: 'Alert is already resolved' },
        { status: 400 }
      );
    }

    // Get request body
    const body = await req.json();

    // Update alert
    const updatedAlert = await db.apiAlert.update({
      where: {
        id: alertId,
      },
      data: {
        resolved: true,
        resolvedAt: new Date(),
        resolvedBy: user.id,
        resolution: body.resolution || null,
      },
    });

    return NextResponse.json(updatedAlert);
  } catch (error) {
    console.error('Error in POST /api/api-alerts/[id]/resolve:', error);
    return NextResponse.json(
      { error: 'Failed to resolve API alert' },
      { status: 500 }
    );
  }
}
