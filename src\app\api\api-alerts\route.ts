import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(req: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const status = searchParams.get('status') || 'all';
    const severity = searchParams.get('severity') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const skip = (page - 1) * limit;

    // Build the where clause
    const where: any = {};
    
    // Filter by status
    if (status === 'active') {
      where.resolved = false;
    } else if (status === 'resolved') {
      where.resolved = true;
    }
    
    // Filter by severity
    if (severity !== 'all') {
      where.severity = severity;
    }
    
    // Get alerts
    const alerts = await db.apiAlert.findMany({
      where,
      orderBy: {
        timestamp: 'desc',
      },
      skip,
      take: limit,
    });

    // Get total count
    const totalAlerts = await db.apiAlert.count({
      where,
    });

    return NextResponse.json(alerts);
  } catch (error) {
    console.error('Error in GET /api/api-alerts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch API alerts' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    
    // Validate required fields
    if (!body.type || !body.severity || !body.message) {
      return NextResponse.json(
        { error: 'Missing required fields: type, severity, message' },
        { status: 400 }
      );
    }
    
    // Create alert
    const alert = await db.apiAlert.create({
      data: {
        type: body.type,
        severity: body.severity,
        message: body.message,
        details: JSON.stringify(body.details || {}),
        apiKeyId: body.apiKeyId,
        userId: body.userId,
      },
    });

    return NextResponse.json(alert);
  } catch (error) {
    console.error('Error in POST /api/api-alerts:', error);
    return NextResponse.json(
      { error: 'Failed to create API alert' },
      { status: 500 }
    );
  }
}
