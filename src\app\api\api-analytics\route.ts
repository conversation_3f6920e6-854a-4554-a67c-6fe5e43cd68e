import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(req: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const apiKeyId = searchParams.get('apiKeyId');
    const timeRange = searchParams.get('timeRange') || '7d';

    // Calculate the start date based on the time range
    let startDate: Date | null = null;
    const now = new Date();

    if (timeRange === '24h') {
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    } else if (timeRange === '7d') {
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    } else if (timeRange === '30d') {
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Build the where clause
    const where: any = {};

    // If apiKeyId is 'all', get usage for all API keys owned by the user
    if (apiKeyId && apiKeyId !== 'all') {
      // Check if the API key belongs to the user
      const apiKey = await db.apiKey.findFirst({
        where: {
          id: apiKeyId,
          userId: user.id!,
        },
      });

      if (!apiKey) {
        return NextResponse.json(
          { error: 'API key not found' },
          { status: 404 }
        );
      }

      where.apiKeyId = apiKeyId;
    } else {
      // Get all API keys owned by the user
      const apiKeys = await db.apiKey.findMany({
        where: {
          userId: user.id!,
        },
        select: {
          id: true,
        },
      });

      where.apiKeyId = {
        in: apiKeys.map(key => key.id),
      };
    }

    // Add time range filter if specified
    if (startDate) {
      where.timestamp = {
        gte: startDate,
      };
    }

    // Get usage data
    const usageData = await db.apiKeyUsage.findMany({
      where,
      orderBy: {
        timestamp: 'desc',
      },
      take: 100, // Limit to 100 records for performance
    });

    // Calculate statistics
    const totalRequests = await db.apiKeyUsage.count({ where });
    const successfulRequests = await db.apiKeyUsage.count({
      where: {
        ...where,
        status: {
          lt: 400,
        },
      },
    });
    const failedRequests = totalRequests - successfulRequests;

    // Get top endpoints
    let topEndpointsRaw: { endpoint: string; count: number }[] = [];

    try {
      if (startDate) {
        topEndpointsRaw = await db.$queryRaw`
          SELECT "endpoint", COUNT(*) as count
          FROM "ApiKeyUsage"
          WHERE "apiKeyId" IN (
            SELECT "id" FROM "ApiKey" WHERE "userId" = ${user.id}
          )
          AND "timestamp" >= ${startDate}
          GROUP BY "endpoint"
          ORDER BY count DESC
          LIMIT 10
        `;
      } else {
        topEndpointsRaw = await db.$queryRaw`
          SELECT "endpoint", COUNT(*) as count
          FROM "ApiKeyUsage"
          WHERE "apiKeyId" IN (
            SELECT "id" FROM "ApiKey" WHERE "userId" = ${user.id}
          )
          GROUP BY "endpoint"
          ORDER BY count DESC
          LIMIT 10
        `;
      }
    } catch (error) {
      console.error('Error getting top endpoints:', error);
      topEndpointsRaw = [];
    }

    // Get requests by method
    let requestsByMethodRaw: { method: string; count: number }[] = [];

    try {
      if (startDate) {
        requestsByMethodRaw = await db.$queryRaw`
          SELECT "method", COUNT(*) as count
          FROM "ApiKeyUsage"
          WHERE "apiKeyId" IN (
            SELECT "id" FROM "ApiKey" WHERE "userId" = ${user.id}
          )
          AND "timestamp" >= ${startDate}
          GROUP BY "method"
          ORDER BY count DESC
        `;
      } else {
        requestsByMethodRaw = await db.$queryRaw`
          SELECT "method", COUNT(*) as count
          FROM "ApiKeyUsage"
          WHERE "apiKeyId" IN (
            SELECT "id" FROM "ApiKey" WHERE "userId" = ${user.id}
          )
          GROUP BY "method"
          ORDER BY count DESC
        `;
      }
    } catch (error) {
      console.error('Error getting requests by method:', error);
      requestsByMethodRaw = [];
    }

    // Get requests by day
    let requestsByDayRaw: { date: string; count: number }[] = [];

    try {
      if (startDate) {
        requestsByDayRaw = await db.$queryRaw`
          SELECT
            DATE_TRUNC('day', "timestamp") as date,
            COUNT(*) as count
          FROM "ApiKeyUsage"
          WHERE "apiKeyId" IN (
            SELECT "id" FROM "ApiKey" WHERE "userId" = ${user.id}
          )
          AND "timestamp" >= ${startDate}
          GROUP BY DATE_TRUNC('day', "timestamp")
          ORDER BY date ASC
        `;
      } else {
        requestsByDayRaw = await db.$queryRaw`
          SELECT
            DATE_TRUNC('day', "timestamp") as date,
            COUNT(*) as count
          FROM "ApiKeyUsage"
          WHERE "apiKeyId" IN (
            SELECT "id" FROM "ApiKey" WHERE "userId" = ${user.id}
          )
          GROUP BY DATE_TRUNC('day', "timestamp")
          ORDER BY date ASC
        `;
      }
    } catch (error) {
      console.error('Error getting requests by day:', error);
      requestsByDayRaw = [];
    }

    // Format the results
    const topEndpoints = topEndpointsRaw.map(item => ({
      endpoint: item.endpoint,
      count: Number(item.count),
    }));

    const requestsByMethod = requestsByMethodRaw.map(item => ({
      method: item.method,
      count: Number(item.count),
    }));

    const requestsByDay = requestsByDayRaw.map(item => ({
      date: new Date(item.date).toISOString(),
      count: Number(item.count),
    }));

    return NextResponse.json({
      usageData,
      stats: {
        totalRequests,
        successfulRequests,
        failedRequests,
        topEndpoints,
        requestsByMethod,
        requestsByDay,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/api-analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch API analytics' },
      { status: 500 }
    );
  }
}
