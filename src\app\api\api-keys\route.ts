import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { generateApiKey } from '@/lib/api-auth';
import { currentUser } from '@/lib/auth';

// GET: Fetch all API keys for the current user
export async function GET(req: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Note: This assumes you've added the ApiKey model to your Prisma schema
    // If you haven't implemented the ApiKey model yet, this will fail
    try {
      const apiKeys = await db.apiKey.findMany({
        where: { userId: user.id },
        select: {
          id: true,
          name: true,
          key: true, // You might want to hide this in production
          permissions: true,
          rateLimit: true,
          usageCount: true,
          lastUsed: true,
          expiresAt: true,
          createdAt: true,
        },
        orderBy: { createdAt: 'desc' },
      });

      return NextResponse.json(apiKeys);
    } catch (error) {
      // If the ApiKey model doesn't exist yet, this will catch the error
      console.error('Error fetching API keys:', error);
      return NextResponse.json(
        { error: 'API key management is not yet implemented' },
        { status: 501 }
      );
    }
  } catch (error) {
    console.error('Error in GET /api/api-keys:', error);
    return NextResponse.json(
      { error: 'Failed to fetch API keys' },
      { status: 500 }
    );
  }
}

// POST: Create a new API key for the current user
export async function POST(req: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { name, permissions, expiresAt, rateLimit } = await req.json();

    if (!name) {
      return NextResponse.json(
        { error: 'API key name is required' },
        { status: 400 }
      );
    }

    // Generate a new API key
    const key = generateApiKey();

    // Note: This assumes you've added the ApiKey model to your Prisma schema
    // If you haven't implemented the ApiKey model yet, this will fail
    try {
      const apiKey = await db.apiKey.create({
        data: {
          key,
          name,
          permissions: permissions || ['read:events'],
          expiresAt: expiresAt ? new Date(expiresAt) : null,
          rateLimit: rateLimit || 100,
          usageCount: 0,
          userId: user.id!,
        },
      });

      return NextResponse.json({
        id: apiKey.id,
        name: apiKey.name,
        key: apiKey.key, // Only return the key once when it's created
        permissions: apiKey.permissions,
        rateLimit: apiKey.rateLimit,
        usageCount: apiKey.usageCount,
        expiresAt: apiKey.expiresAt,
        createdAt: apiKey.createdAt,
      });
    } catch (error) {
      // If the ApiKey model doesn't exist yet, this will catch the error
      console.error('Error creating API key:', error);
      return NextResponse.json(
        { error: 'API key management is not yet implemented' },
        { status: 501 }
      );
    }
  } catch (error) {
    console.error('Error in POST /api/api-keys:', error);
    return NextResponse.json(
      { error: 'Failed to create API key' },
      { status: 500 }
    );
  }
}

// DELETE: Delete an API key
export async function DELETE(req: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'API key ID is required' },
        { status: 400 }
      );
    }

    // Note: This assumes you've added the ApiKey model to your Prisma schema
    // If you haven't implemented the ApiKey model yet, this will fail
    try {
      // Make sure the API key belongs to the current user
      const apiKey = await db.apiKey.findFirst({
        where: {
          id,
          userId: user.id!,
        },
      });

      if (!apiKey) {
        return NextResponse.json(
          { error: 'API key not found' },
          { status: 404 }
        );
      }

      // Delete the API key
      await db.apiKey.delete({
        where: { id },
      });

      return NextResponse.json({ success: true });
    } catch (error) {
      // If the ApiKey model doesn't exist yet, this will catch the error
      console.error('Error deleting API key:', error);
      return NextResponse.json(
        { error: 'API key management is not yet implemented' },
        { status: 501 }
      );
    }
  } catch (error) {
    console.error('Error in DELETE /api/api-keys:', error);
    return NextResponse.json(
      { error: 'Failed to delete API key' },
      { status: 500 }
    );
  }
}
