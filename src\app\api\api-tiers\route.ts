import { NextRequest, NextResponse } from 'next/server';
import { getApiTiers } from '@/lib/api-tier-utils';

/**
 * GET /api/api-tiers
 * Get API tiers information
 */
export async function GET(request: NextRequest) {
  try {
    const tiers = await getApiTiers();
    
    return NextResponse.json({
      success: true,
      data: tiers
    });
  } catch (error) {
    console.error('Error fetching API tiers:', error);
    
    return NextResponse.json(
      { error: 'Failed to fetch API tiers' },
      { status: 500 }
    );
  }
}
