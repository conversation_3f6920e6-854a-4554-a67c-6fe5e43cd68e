import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/prisma";
import { currentUser } from "@/lib/auth";

// GET: Retrieve audience segments
export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get URL parameters for filtering
    const url = new URL(request.url);
    const type = url.searchParams.get('type');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const page = parseInt(url.searchParams.get('page') || '1');
    const skip = (page - 1) * limit;

    // Build query
    const where = {
      userId: user.id!,
      ...(type ? { type } : {}),
    };

    // Fetch segments with real data
    // First, get all users who have purchased tickets
    const ticketBuyers = await db.user.findMany({
      where: {
        orders: {
          some: {
            status: 'Completed',
          },
        },
      },
      select: {
        id: true,
      },
    });

    // Get users who have attended events
    const eventAttendees = await db.user.findMany({
      where: {
        tickets: {
          some: {
            isUsed: true,
          },
        },
      },
      select: {
        id: true,
      },
    });

    // Get users who have created events
    const eventOrganizers = await db.user.findMany({
      where: {
        role: 'ORGANIZER',
        events: {
          some: {},
        },
      },
      select: {
        id: true,
      },
    });

    // Get users who registered in the last 30 days
    const recentUsers = await db.user.findMany({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        },
      },
      select: {
        id: true,
      },
    });

    // Create segments based on real data
    const segments = [
      {
        id: 'ticket-buyers',
        name: 'Ticket Buyers',
        description: 'Users who have purchased tickets',
        count: ticketBuyers.length,
        type: 'system',
      },
      {
        id: 'event-attendees',
        name: 'Event Attendees',
        description: 'Users who have attended events',
        count: eventAttendees.length,
        type: 'system',
      },
      {
        id: 'event-organizers',
        name: 'Event Organizers',
        description: 'Users who have created events',
        count: eventOrganizers.length,
        type: 'system',
      },
      {
        id: 'recent-users',
        name: 'Recent Users',
        description: 'Users who registered in the last 30 days',
        count: recentUsers.length,
        type: 'system',
      },
    ];

    // Get any custom segments created by the user
    const customSegments = await db.audienceSegment.findMany({
      where: {
        userId: user.id!,
      },
      select: {
        id: true,
        name: true,
        description: true,
        type: true,
        _count: {
          select: {
            users: true,
          },
        },
      },
    });

    // Combine system and custom segments
    const allSegments = [
      ...segments,
      ...customSegments.map(segment => ({
        id: segment.id,
        name: segment.name,
        description: segment.description || '',
        count: segment._count.users,
        type: segment.type || 'custom',
      })),
    ];

    return NextResponse.json({
      segments: allSegments,
      pagination: {
        total: allSegments.length,
        page,
        limit,
        pages: Math.ceil(allSegments.length / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching audience segments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch audience segments' },
      { status: 500 }
    );
  }
}

// POST: Create a new audience segment
export async function POST(request: NextRequest) {
  try {
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const {
      name,
      description,
      type,
      criteria,
      userIds,
    } = await request.json();

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // Create the segment
    const segment = await db.audienceSegment.create({
      data: {
        name,
        description: description || '',
        type: type || 'custom',
        criteria: criteria || {},
        userId: user.id!,
        users: {
          connect: userIds?.map((id: string) => ({ id })) || [],
        },
      },
    });

    return NextResponse.json(segment, { status: 201 });
  } catch (error) {
    console.error('Error creating audience segment:', error);
    return NextResponse.json(
      { error: 'Failed to create audience segment' },
      { status: 500 }
    );
  }
}
