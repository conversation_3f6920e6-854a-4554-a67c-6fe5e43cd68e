import { NextRequest, NextResponse } from 'next/server';

// Force this route to be dynamic
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  // Clear all cookies related to authentication
  const authCookies = [
    'next-auth.session-token',
    'next-auth.callback-url',
    'next-auth.csrf-token',
    '__Secure-next-auth.callback-url',
    '__Secure-next-auth.session-token',
    '__Secure-next-auth.csrf-token',
    '__Host-next-auth.csrf-token',
  ];

  const response = NextResponse.redirect(new URL('/', request.url));

  // Clear each cookie
  authCookies.forEach(cookieName => {
    const cookie = request.cookies.get(cookieName);
    if (cookie) {
      response.cookies.delete(cookieName);
    }
  });

  return response;
}
