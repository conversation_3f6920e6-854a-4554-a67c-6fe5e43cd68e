import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/auth';

/**
 * API endpoint to handle OAuth redirects
 * This ensures that users are redirected to the correct page after OAuth login
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current user from the session
    const session = await getSession();

    // Default redirect path
    let redirectPath = '/dashboard';

    // Get the callbackUrl from the query string
    const callbackUrl = request.nextUrl.searchParams.get('callbackUrl');

    // Log the session for debugging
    console.log("OAuth redirect route called, user role:", session?.user?.role);

    // If there's a callbackUrl, use it
    if (callbackUrl) {
      redirectPath = callbackUrl;
    } else if (session?.user?.role) {
      // Otherwise, redirect based on user role
      switch (session.user.role) {
        case 'ADMIN':
          redirectPath = '/admin/dashboard';
          break;
        case 'USER':
          redirectPath = '/dashboard/user';
          break;
        case 'VENDOR':
          redirectPath = '/dashboard/vendor';
          break;
        case 'ORGANIZER':
          redirectPath = '/dashboard/organizer';
          break;
        case 'SUPERADMIN':
          redirectPath = '/admin/dashboard';
          break;
        case 'DEVELOPER':
          redirectPath = '/dashboard/developer';
          break;
        case 'PARTNER':
          console.log("Redirecting PARTNER to /dashboard/partner");
          redirectPath = '/dashboard/partner';
          break;
        default:
          redirectPath = '/dashboard';
          break;
      }
    }

    // Log the redirect path
    console.log("OAuth redirecting to:", redirectPath);

    // Redirect to the appropriate dashboard
    return NextResponse.redirect(new URL(redirectPath, request.url));
  } catch (error) {
    console.error('Error in OAuth redirect route:', error);

    // If there's an error, redirect to the default dashboard
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }
}
