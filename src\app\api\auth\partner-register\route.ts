import { NextResponse } from 'next/server';
import { hash } from 'bcryptjs';
import { db } from '@/lib/prisma';
import { generateVerificationToken } from '@/data/tokens';
import { sendPartnerRegistrationEmail } from '@/lib/mail';

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { name, email, password, businessName, partnerType, contactPhone } = body;

    // Validate inputs
    if (!name || !email || !password || !businessName || !partnerType || !contactPhone) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate partner type
    const validPartnerTypes = ['HOTEL', 'RESTAURANT', 'BAR', 'NIGHTCLUB'];
    if (!validPartnerTypes.includes(partnerType)) {
      return NextResponse.json(
        { error: 'Invalid partner type selected' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await db.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'Email already in use' },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await hash(password, 10);

    // Create user with PARTNER role
    const user = await db.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role: 'PARTNER',
      }
    });

    console.log(`Created partner user with email ${email}`);

    // Create partner profile
    const partner = await db.partner.create({
      data: {
        businessName,
        partnerType,
        contactName: name,
        contactEmail: email,
        contactPhone,
        userId: user.id!,
        address: '',
        city: '',
        province: '',
      }
    });

    console.log(`Created partner profile for ${businessName}`);

    // Generate verification token
    const verificationToken = await generateVerificationToken(email);

    // Send partner registration email with verification token and password
    await sendPartnerRegistrationEmail(
      email,
      verificationToken.token,
      password, // Send the original password in the email
      businessName,
      partnerType
    );

    return NextResponse.json(
      {
        success: 'Partner registration successful! Please check your email for verification and login details.',
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Partner registration error:', error);
    return NextResponse.json(
      { error: 'Something went wrong during partner registration' },
      { status: 500 }
    );
  }
}
