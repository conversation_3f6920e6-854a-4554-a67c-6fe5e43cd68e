import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/auth';

export async function GET() {
  try {
    const session = await getSession();

    // Ensure we always return a valid JSON object, even if session is null
    if (!session) {
      return NextResponse.json({
        user: null,
        expires: null
      });
    }

    return NextResponse.json(session);
  } catch (error) {
    console.error('Error fetching session:', error);
    // Return a properly structured session object even on error
    return NextResponse.json({
      user: null,
      expires: null,
      error: 'Failed to fetch session'
    }, { status: 500 });
  }
}

// Add POST method to handle session updates
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();

    // Ensure we always return a valid JSON object, even if session is null
    if (!session) {
      return NextResponse.json({
        user: null,
        expires: null
      });
    }

    // Return the current session
    return NextResponse.json(session);
  } catch (error) {
    console.error('Error updating session:', error);
    // Return a properly structured session object even on error
    return NextResponse.json({
      user: null,
      expires: null,
      error: 'Failed to update session'
    }, { status: 500 });
  }
}
