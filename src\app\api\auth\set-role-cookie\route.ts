import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/auth';

// Force this route to be dynamic
export const dynamic = 'force-dynamic';

/**
 * API route to set a user-role cookie based on the user's role in the session
 * This is used by the middleware to determine if a user is a partner
 */
export async function GET(request: NextRequest) {
  try {
    // Get the user's session
    const session = await getSession();

    // If the user has a role, set a cookie with that role
    if (session?.user?.role) {
      // Create response object
      const response = NextResponse.json({ success: true, role: session.user.role });

      // Set a cookie with the user's role using NextResponse
      // This cookie will be used by the middleware to determine if a user is a partner
      response.cookies.set('user-role', session.user.role, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 7 * 24 * 60 * 60, // 7 days in seconds (match session duration)
        path: '/',
      });

      return response;
    }

    // If the user doesn't have a role, return a 404
    return NextResponse.json({ error: 'No user role found' }, { status: 404 });
  } catch (error) {
    console.error('Error setting role cookie:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
