import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@/lib/auth';
import { db } from '@/lib/prisma';
import crypto from 'crypto';

/**
 * API endpoint to synchronize the current user with the database
 * This ensures that every authenticated user has a corresponding database record
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current user from the session
    const user = await currentUser();

    // If there's no authenticated user, return a more graceful response
    if (!user?.id) {
      return NextResponse.json({
        success: false,
        message: 'Not authenticated',
        requiresAuth: true
      }, { status: 200 }); // Use 200 instead of 401 to avoid console errors
    }

    // Check if the user exists in the database
    const dbUser = await db.user.findUnique({
      where: { id: user.id },
    });

    // If the user doesn't exist in the database, create them
    if (!dbUser) {
      // Check if a user with the same email already exists
      const existingUserByEmail = user.email ? await db.user.findUnique({
        where: { email: user.email },
      }) : null;

      // If a user with the same email exists, we'll use that user's ID instead
      if (existingUserByEmail) {
        console.log(`User with email ${user.email} already exists with ID ${existingUserByEmail.id}, but session has ID ${user.id}`);

        // Return the existing user's information
        return NextResponse.json({
          success: true,
          message: 'User account is synchronized with existing email',
          user: {
            id: existingUserByEmail.id,
            email: existingUserByEmail.email,
            name: existingUserByEmail.name,
            role: existingUserByEmail.role,
          },
          // Include a flag indicating that the client should update the session
          updateSession: true,
          existingUserId: existingUserByEmail.id,
        });
      }

      try {
        // Generate a random access token
        const accessToken = crypto.randomBytes(32).toString('hex');

        // Create a new user with the session data
        const newUser = await db.user.create({
          data: {
            id: user.id,
            name: user.name || 'User',
            email: user.email || `user-${Date.now()}@example.com`,
            role: user.role || 'USER',
            emailVerified: new Date(),
            image: user.image,
            accessToken,
          },
        });

        console.log(`Created new user with ID: ${newUser.id}`);

        return NextResponse.json({
          success: true,
          message: 'User account created successfully',
          user: {
            id: newUser.id,
            email: newUser.email,
            name: newUser.name,
            role: newUser.role,
          },
        });
      } catch (error) {
        console.error('Error creating user:', error);

        return NextResponse.json({
          success: false,
          message: 'Failed to create user account',
          details: error instanceof Error ? error.message : 'Unknown error',
        }, { status: 500 });
      }
    }

    // Check if user has an access token
    if (!dbUser.accessToken) {
      try {
        // Generate a random access token
        const accessToken = crypto.randomBytes(32).toString('hex');

        // Update the user with the access token
        await db.user.update({
          where: { id: user.id },
          data: { accessToken },
        });

        console.log(`Updated access token for user: ${user.id}`);
      } catch (error) {
        console.error('Error updating access token:', error);
        // Continue even if access token update fails
      }
    }

    // User exists in the database
    return NextResponse.json({
      success: true,
      message: 'User account is synchronized',
      user: {
        id: dbUser.id,
        email: dbUser.email,
        name: dbUser.name,
        role: dbUser.role,
      },
    });
  } catch (error) {
    console.error('Error synchronizing user:', error);

    return NextResponse.json({
      success: false,
      message: 'Failed to synchronize user',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
