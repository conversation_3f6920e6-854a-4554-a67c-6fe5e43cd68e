import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    console.log('Create Test User - Starting request');
    
    // Get the current user from the session
    const sessionUser = await currentUser();
    console.log('Create Test User - Session user:', sessionUser ? { id: sessionUser.id, email: sessionUser.email } : 'Not authenticated');
    
    if (!sessionUser?.id) {
      return NextResponse.json({
        success: false,
        message: 'Not authenticated',
      }, { status: 401 });
    }
    
    // Check if the user already exists in the database
    let dbUser = await db.user.findUnique({
      where: { id: sessionUser.id },
    });
    
    if (dbUser) {
      console.log('Create Test User - User already exists in database:', dbUser.id);
      return NextResponse.json({
        success: true,
        message: 'User already exists in database',
        user: {
          id: dbUser.id,
          email: dbUser.email,
          name: dbUser.name,
          role: dbUser.role,
        },
        created: false,
      });
    }
    
    // Create the user in the database
    try {
      console.log('Create Test User - Creating user in database with ID:', sessionUser.id);
      dbUser = await db.user.create({
        data: {
          id: sessionUser.id,
          email: sessionUser.email || '<EMAIL>',
          name: sessionUser.name || 'Test User',
          role: 'VENDOR', // Set role to VENDOR for NFC settings
        },
      });
      
      console.log('Create Test User - User created successfully:', dbUser.id);
      return NextResponse.json({
        success: true,
        message: 'User created successfully',
        user: {
          id: dbUser.id,
          email: dbUser.email,
          name: dbUser.name,
          role: dbUser.role,
        },
        created: true,
      });
    } catch (error) {
      console.error('Create Test User - Error creating user:', error);
      return NextResponse.json({
        success: false,
        message: 'Failed to create user',
        error: error instanceof Error ? error.message : 'Unknown error',
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Create Test User - Unexpected error:', error);
    return NextResponse.json({
      success: false,
      message: 'Unexpected error',
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
