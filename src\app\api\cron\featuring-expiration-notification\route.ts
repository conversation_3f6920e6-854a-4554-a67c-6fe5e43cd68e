import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { addDays, format } from 'date-fns';
import { sendEmail } from '@/lib/email';

/**
 * This endpoint sends notifications for featuring that will expire soon.
 * It should be called by a cron job daily.
 */
export async function GET(request: NextRequest) {
  try {
    // Verify cron secret to prevent unauthorized access
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    if (!cronSecret || authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const now = new Date();
    
    // Find featuring records that will expire in 3 days
    const expirationDate = addDays(now, 3);
    const startOfDay = new Date(now.setHours(0, 0, 0, 0));
    const endOfDay = new Date(now.setHours(23, 59, 59, 999));
    
    const expiringFeaturings = await db.eventFeaturing.findMany({
      where: {
        status: 'ACTIVE',
        endDate: {
          gte: startOfDay,
          lte: expirationDate,
        },
      },
      include: {
        event: {
          select: {
            id: true,
            title: true,
            userId: true,
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });
    
    console.log(`Found ${expiringFeaturings.length} featuring records expiring soon`);
    
    // Track successful and failed notifications
    const results = {
      total: expiringFeaturings.length,
      successful: 0,
      failed: 0,
      details: [] as any[],
    };
    
    // Send notifications for featuring records that will expire soon
    for (const featuring of expiringFeaturings) {
      try {
        // Check if a notification has already been sent today
        const existingNotification = await db.notification.findFirst({
          where: {
            userId: featuring.event.userId,
            type: 'FEATURING_EXPIRING_SOON',
            createdAt: {
              gte: startOfDay,
            },
            message: {
              contains: featuring.event.title,
              mode: 'insensitive' as const,
            },
          },
        });
        
        if (!existingNotification) {
          // Create notification in the database
          await db.notification.create({
            data: {
              userId: featuring.event.userId,
              message: `Your event "${featuring.event.title}" will stop being featured on ${format(featuring.endDate, 'MMMM dd, yyyy')}. Renew now to maintain visibility.`,
              type: 'FEATURING_EXPIRING_SOON',
              isRead: false,
            },
          });
          
          // Send email notification
          if (featuring.event.user?.email) {
            await sendEmail({
              to: featuring.event.user.email,
              subject: `Your Featured Event is Expiring Soon: ${featuring.event.title}`,
              html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                  <h2>Your Featured Event is Expiring Soon</h2>
                  <p>Hello ${featuring.event.user.name || 'there'},</p>
                  <p>Your event <strong>${featuring.event.title}</strong> is currently featured on our platform, but this promotion will expire on <strong>${format(featuring.endDate, 'MMMM dd, yyyy')}</strong>.</p>
                  <p>To maintain your event's visibility and continue receiving premium placement on our platform, we recommend renewing your featuring before it expires.</p>
                  <div style="margin: 30px 0; text-align: center;">
                    <a href="${process.env.NEXT_PUBLIC_APP_URL}/dashboard/events/${featuring.event.id}" style="background-color: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Renew Featuring Now</a>
                  </div>
                  <p>Benefits of featuring your event:</p>
                  <ul>
                    <li>Premium placement on the landing page</li>
                    <li>Higher visibility to potential attendees</li>
                    <li>Increased ticket sales</li>
                    <li>Detailed analytics and performance tracking</li>
                  </ul>
                  <p>If you have any questions, please don't hesitate to contact our support team.</p>
                  <p>Best regards,<br>The Event Platform Team</p>
                </div>
              `,
            });
          }
          
          results.successful++;
          results.details.push({
            eventId: featuring.event.id,
            eventTitle: featuring.event.title,
            userId: featuring.event.userId,
            userEmail: featuring.event.user?.email,
            expirationDate: featuring.endDate,
            status: 'success',
          });
          
          console.log(`Sent expiration notification for event: ${featuring.event.title}`);
        } else {
          console.log(`Notification already sent today for event: ${featuring.event.title}`);
        }
      } catch (error) {
        console.error(`Error sending notification for event ${featuring.event.id}:`, error);
        results.failed++;
        results.details.push({
          eventId: featuring.event.id,
          eventTitle: featuring.event.title,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }
    
    return NextResponse.json({
      success: true,
      results,
    });
  } catch (error) {
    console.error('Error processing featuring expiration notifications:', error);
    return NextResponse.json(
      { error: 'Failed to process featuring expiration notifications' },
      { status: 500 }
    );
  }
}
