import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { format } from 'date-fns';
import { sendEmail } from '@/lib/email';

/**
 * This endpoint handles the expiration of featured events.
 * It should be called by a cron job daily.
 */
export async function GET(request: NextRequest) {
  try {
    // Verify cron secret to prevent unauthorized access
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;

    if (!cronSecret || authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const now = new Date();

    // Find all active featuring records that have expired
    const expiredFeaturings = await db.eventFeaturing.findMany({
      where: {
        status: 'ACTIVE',
        endDate: {
          lt: now,
        },
      },
      include: {
        event: {
          select: {
            id: true,
            title: true,
            userId: true,
          },
        },
      },
    });

    console.log(`Found ${expiredFeaturings.length} expired featuring records`);

    // Update the status of expired featuring records
    for (const featuring of expiredFeaturings) {
      // Update featuring status
      await db.eventFeaturing.update({
        where: { id: featuring.id },
        data: { status: 'EXPIRED' },
      });

      // Update event metadata
      await db.event.update({
        where: { id: featuring.event.id },
        data: {
          metadata: {
            isFeatured: false,
          },
        },
      });

      try {
        // Get the event owner details
        const eventOwner = await db.user.findUnique({
          where: { id: featuring.event.userId },
          select: { name: true, email: true },
        });

        // Create notification for the event owner
        await db.notification.create({
          data: {
            userId: featuring.event.userId,
            message: `Your event "${featuring.event.title}" is no longer featured. Renew featuring to maintain visibility.`,
            type: 'FEATURING_EXPIRED',
            isRead: false,
          },
        });

        // Send email notification if the user has an email
        if (eventOwner?.email) {
          await sendEmail({
            to: eventOwner.email,
            subject: `Your Event is No Longer Featured: ${featuring.event.title}`,
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>Your Event is No Longer Featured</h2>
                <p>Hello ${eventOwner.name || 'there'},</p>
                <p>Your event <strong>${featuring.event.title}</strong> is no longer featured on our platform as of <strong>${format(new Date(), 'MMMM dd, yyyy')}</strong>.</p>
                <p>To restore your event's visibility and premium placement on our platform, you can renew your featuring at any time.</p>
                <div style="margin: 30px 0; text-align: center;">
                  <a href="${process.env.NEXT_PUBLIC_APP_URL}/dashboard/events/${featuring.event.id}" style="background-color: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Renew Featuring Now</a>
                </div>
                <p>Benefits of featuring your event:</p>
                <ul>
                  <li>Premium placement on the landing page</li>
                  <li>Higher visibility to potential attendees</li>
                  <li>Increased ticket sales</li>
                  <li>Detailed analytics and performance tracking</li>
                </ul>
                <p>If you have any questions, please don't hesitate to contact our support team.</p>
                <p>Best regards,<br>The Event Platform Team</p>
              </div>
            `,
          });
        }

        console.log(`Expired featuring for event: ${featuring.event.title}`);
      } catch (error) {
        console.error(`Error processing expiration for event ${featuring.event.id}:`, error);
      }
    }

    // Find featuring records that will expire soon (in 3 days)
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + 3);

    const expiringFeaturings = await db.eventFeaturing.findMany({
      where: {
        status: 'ACTIVE',
        endDate: {
          gte: now,
          lte: expirationDate,
        },
      },
      include: {
        event: {
          select: {
            id: true,
            title: true,
            userId: true,
          },
        },
      },
    });

    console.log(`Found ${expiringFeaturings.length} featuring records expiring soon`);

    // Send notifications for featuring records that will expire soon
    for (const featuring of expiringFeaturings) {
      // Check if a notification has already been sent
      const existingNotification = await db.notification.findFirst({
        where: {
          userId: featuring.event.userId,
          type: 'FEATURING_EXPIRING_SOON',
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          },
          message: {
            contains: featuring.event.title,
          },
        },
      });

      if (!existingNotification) {
        // Create notification for the event owner
        await db.notification.create({
          data: {
            userId: featuring.event.userId,
            message: `Your event "${featuring.event.title}" will stop being featured in 3 days. Renew now to maintain visibility.`,
            type: 'FEATURING_EXPIRING_SOON',
            isRead: false,
          },
        });

        console.log(`Sent expiration notification for event: ${featuring.event.title}`);
      }
    }

    return NextResponse.json({
      success: true,
      expiredCount: expiredFeaturings.length,
      expiringCount: expiringFeaturings.length,
    });
  } catch (error) {
    console.error('Error processing featuring expiration:', error);
    return NextResponse.json(
      { error: 'Failed to process featuring expiration' },
      { status: 500 }
    );
  }
}
