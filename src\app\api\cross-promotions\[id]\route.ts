import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/cross-promotions/{id}
 * Get details about a specific cross-promotion
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    // Find the cross-promotion
    const crossPromotion = await db.crossPromotion.findUnique({
      where: { id },
      include: {
        sourceEvent: {
          select: {
            id: true,
            title: true,
            description: true,
            imagePath: true,
            startDate: true,
            location: true,
            userId: true,
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              }
            }
          }
        },
        targetEvent: {
          select: {
            id: true,
            title: true,
            description: true,
            imagePath: true,
            startDate: true,
            location: true,
            userId: true,
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              }
            }
          }
        }
      }
    });

    if (!crossPromotion) {
      return NextResponse.json(
        { error: 'Cross-promotion not found' },
        { status: 404 }
      );
    }

    // Check if the user is involved in this cross-promotion
    const isSourceEventOwner = crossPromotion.sourceEvent.userId === user.id;
    const isTargetEventOwner = crossPromotion.targetEvent.userId === user.id;

    if (!isSourceEventOwner && !isTargetEventOwner) {
      return NextResponse.json(
        { error: 'You are not involved in this cross-promotion' },
        { status: 403 }
      );
    }

    // Format the response
    const formattedCrossPromotion = {
      ...crossPromotion,
      isSourceEventOwner,
      isTargetEventOwner,
    };

    return NextResponse.json(formattedCrossPromotion);
  } catch (error) {
    console.error('Error fetching cross-promotion:', error);
    return NextResponse.json(
      { error: 'Failed to fetch cross-promotion' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/cross-promotions/{id}
 * Update the status of a cross-promotion
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const { status, message } = await request.json();

    // Validate input
    if (!status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }

    // Check if the status is valid
    const validStatuses = ['PENDING', 'ACCEPTED', 'REJECTED', 'ACTIVE', 'COMPLETED', 'CANCELLED'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: `Invalid status. Must be one of: ${validStatuses.join(', ')}` },
        { status: 400 }
      );
    }

    // Find the cross-promotion
    const crossPromotion = await db.crossPromotion.findUnique({
      where: { id },
      include: {
        sourceEvent: {
          select: {
            userId: true,
          }
        },
        targetEvent: {
          select: {
            userId: true,
          }
        }
      }
    });

    if (!crossPromotion) {
      return NextResponse.json(
        { error: 'Cross-promotion not found' },
        { status: 404 }
      );
    }

    // Check if the user is the target event owner (the one who needs to accept/reject)
    const isTargetEventOwner = crossPromotion.targetEvent.userId === user.id;
    const isSourceEventOwner = crossPromotion.sourceEvent.userId === user.id;

    if (!isTargetEventOwner && !isSourceEventOwner) {
      return NextResponse.json(
        { error: 'You are not involved in this cross-promotion' },
        { status: 403 }
      );
    }

    // Only the target event owner can accept/reject a pending request
    if (crossPromotion.status === 'PENDING' && status !== 'PENDING' && !isTargetEventOwner) {
      return NextResponse.json(
        { error: 'Only the target event owner can accept or reject a pending request' },
        { status: 403 }
      );
    }

    // Either party can cancel an active promotion
    if (crossPromotion.status === 'ACTIVE' && status === 'CANCELLED' && !isTargetEventOwner && !isSourceEventOwner) {
      return NextResponse.json(
        { error: 'Only involved parties can cancel an active promotion' },
        { status: 403 }
      );
    }

    // Update the cross-promotion
    const updatedCrossPromotion = await db.crossPromotion.update({
      where: { id },
      data: {
        status,
        responseMessage: message || null,
        respondedBy: user.id,
        respondedAt: new Date(),
      },
    });

    return NextResponse.json(updatedCrossPromotion);
  } catch (error) {
    console.error('Error updating cross-promotion:', error);
    return NextResponse.json(
      { error: 'Failed to update cross-promotion' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/cross-promotions/{id}
 * Delete a cross-promotion
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    // Find the cross-promotion
    const crossPromotion = await db.crossPromotion.findUnique({
      where: { id },
      include: {
        sourceEvent: {
          select: {
            userId: true,
          }
        },
        targetEvent: {
          select: {
            userId: true,
          }
        }
      }
    });

    if (!crossPromotion) {
      return NextResponse.json(
        { error: 'Cross-promotion not found' },
        { status: 404 }
      );
    }

    // Check if the user is involved in this cross-promotion
    const isSourceEventOwner = crossPromotion.sourceEvent.userId === user.id;
    const isTargetEventOwner = crossPromotion.targetEvent.userId === user.id;

    if (!isSourceEventOwner && !isTargetEventOwner) {
      return NextResponse.json(
        { error: 'You are not involved in this cross-promotion' },
        { status: 403 }
      );
    }

    // Only the creator can delete a pending request
    if (crossPromotion.status === 'PENDING' && crossPromotion.createdBy !== user.id) {
      return NextResponse.json(
        { error: 'Only the creator can delete a pending request' },
        { status: 403 }
      );
    }

    // Delete the cross-promotion
    await db.crossPromotion.delete({
      where: { id },
    });

    return NextResponse.json({ success: true }, { status: 200 });
  } catch (error) {
    console.error('Error deleting cross-promotion:', error);
    return NextResponse.json(
      { error: 'Failed to delete cross-promotion' },
      { status: 500 }
    );
  }
}
