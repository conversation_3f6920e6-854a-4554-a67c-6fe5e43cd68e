import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/cross-promotions
 * Get cross-promotions for the current user's events
 */
export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get URL parameters for filtering and pagination
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const eventId = url.searchParams.get('eventId');
    const status = url.searchParams.get('status');
    
    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build where clause for user's events
    const whereEvents: any = {
      userId: user.id!,
    };

    if (eventId) {
      whereEvents.id = eventId;
    }

    // Get user's events
    const userEvents = await db.event.findMany({
      where: whereEvents,
      select: {
        id: true,
      },
    });

    const userEventIds = userEvents.map(event => event.id);

    // Build where clause for cross-promotions
    const whereCrossPromotions: any = {
      OR: [
        { sourceEventId: { in: userEventIds } },
        { targetEventId: { in: userEventIds } },
      ],
    };

    if (status) {
      whereCrossPromotions.status = status;
    }

    // Get total count for pagination
    const total = await db.crossPromotion.count({
      where: whereCrossPromotions,
    });

    // Get cross-promotions with pagination
    const crossPromotions = await db.crossPromotion.findMany({
      where: whereCrossPromotions,
      include: {
        sourceEvent: {
          select: {
            id: true,
            title: true,
            imagePath: true,
            startDate: true,
            userId: true,
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              }
            }
          }
        },
        targetEvent: {
          select: {
            id: true,
            title: true,
            imagePath: true,
            startDate: true,
            userId: true,
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit,
    });

    // Format the response to indicate which events are owned by the current user
    const formattedCrossPromotions = crossPromotions.map(promotion => ({
      ...promotion,
      isSourceEventOwner: promotion.sourceEvent.userId === user.id,
      isTargetEventOwner: promotion.targetEvent.userId === user.id,
    }));

    return NextResponse.json({
      crossPromotions: formattedCrossPromotions,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      }
    });
  } catch (error) {
    console.error('Error fetching cross-promotions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch cross-promotions' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/cross-promotions
 * Create a new cross-promotion request
 */
export async function POST(request: NextRequest) {
  try {
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { sourceEventId, targetEventId, message, promotionType } = await request.json();

    // Validate input
    if (!sourceEventId || !targetEventId) {
      return NextResponse.json(
        { error: 'Source and target event IDs are required' },
        { status: 400 }
      );
    }

    // Check if the source event exists and belongs to the current user
    const sourceEvent = await db.event.findUnique({
      where: { id: sourceEventId },
    });

    if (!sourceEvent) {
      return NextResponse.json(
        { error: 'Source event not found' },
        { status: 404 }
      );
    }

    if (sourceEvent.userId !== user.id) {
      return NextResponse.json(
        { error: 'You do not own the source event' },
        { status: 403 }
      );
    }

    // Check if the target event exists
    const targetEvent = await db.event.findUnique({
      where: { id: targetEventId },
    });

    if (!targetEvent) {
      return NextResponse.json(
        { error: 'Target event not found' },
        { status: 404 }
      );
    }

    // Check if a cross-promotion already exists between these events
    const existingPromotion = await db.crossPromotion.findFirst({
      where: {
        OR: [
          {
            sourceEventId,
            targetEventId,
          },
          {
            sourceEventId: targetEventId,
            targetEventId: sourceEventId,
          }
        ]
      }
    });

    if (existingPromotion) {
      return NextResponse.json(
        { error: 'A cross-promotion already exists between these events', existingPromotion },
        { status: 400 }
      );
    }

    // Create a new cross-promotion
    const crossPromotion = await db.crossPromotion.create({
      data: {
        sourceEventId,
        targetEventId,
        message: message || '',
        promotionType: promotionType || 'MUTUAL',
        status: 'PENDING',
        createdBy: user.id,
      },
    });

    return NextResponse.json(crossPromotion, { status: 201 });
  } catch (error) {
    console.error('Error creating cross-promotion:', error);
    return NextResponse.json(
      { error: 'Failed to create cross-promotion' },
      { status: 500 }
    );
  }
}
