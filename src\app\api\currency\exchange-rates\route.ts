import { NextRequest, NextResponse } from 'next/server';

// Define currency info type
type CurrencyInfo = {
  code: string;
  symbol: string;
  name: string;
  rate: number;
};

// Map of currency codes to symbols and names
const currencySymbols: Record<string, { symbol: string; name: string }> = {
  USD: { symbol: '$', name: 'US Dollar' },
  EUR: { symbol: '€', name: 'Euro' },
  GBP: { symbol: '£', name: 'British Pound' },
  JPY: { symbol: '¥', name: 'Japanese Yen' },
  CAD: { symbol: 'C$', name: 'Canadian Dollar' },
  AUD: { symbol: 'A$', name: 'Australian Dollar' },
  ZMW: { symbol: 'K', name: 'Zambian <PERSON>cha' },
  NGN: { symbol: '₦', name: 'Nigerian Naira' },
  ZAR: { symbol: 'R', name: 'South African Rand' },
  KES: { symbol: 'KSh', name: 'Kenyan Shilling' },
  GHS: { symbol: 'GH₵', name: 'Ghanaian <PERSON>' },
  INR: { symbol: '₹', name: 'Indian Rupee' },
  CNY: { symbol: '¥', name: 'Chinese Yuan' },
  BRL: { symbol: 'R$', name: 'Brazilian Real' },
  RUB: { symbol: '₽', name: 'Russian Ruble' },
  MXN: { symbol: 'Mex$', name: 'Mexican Peso' },
};

// European countries that use EUR
const euroCountries = [
  'AT', 'BE', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IE', 'IT',
  'LV', 'LT', 'LU', 'MT', 'NL', 'PT', 'SK', 'SI', 'ES'
];

// Map country codes to currencies
const countryCurrencyMap: Record<string, string> = {
  US: 'USD',
  CA: 'CAD',
  GB: 'GBP',
  JP: 'JPY',
  AU: 'AUD',
  ZM: 'ZMW',
  NG: 'NGN',
  ZA: 'ZAR',
  KE: 'KES',
  GH: 'GHS',
  IN: 'INR',
  CN: 'CNY',
  BR: 'BRL',
  RU: 'RUB',
  MX: 'MXN',
};

// Add Euro countries to the map
euroCountries.forEach(country => {
  countryCurrencyMap[country] = 'EUR';
});

export async function GET(request: NextRequest) {
  try {
    // Get IP address from request headers
    const forwardedFor = request.headers.get('x-forwarded-for');
    const ip = forwardedFor ? forwardedFor.split(',')[0] : '127.0.0.1';
    
    // Call external API to get location data
    const geoResponse = await fetch(`https://ipapi.co/${ip}/json/`);
    
    let countryCode = 'US'; // Default to US
    let countryName = 'United States';
    
    if (geoResponse.ok) {
      const geoData = await geoResponse.json();
      countryCode = geoData.country_code || countryCode;
      countryName = geoData.country_name || countryName;
    }
    
    // Get currency code for the country
    const currencyCode = countryCurrencyMap[countryCode] || 'USD';
    
    // Fetch exchange rates from free API
    // Using fawazahmed0's currency API which is free and has no rate limits
    const ratesResponse = await fetch(`https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies/usd.json`);
    
    if (!ratesResponse.ok) {
      throw new Error(`Failed to fetch exchange rates: ${ratesResponse.status}`);
    }
    
    const ratesData = await ratesResponse.json();
    const rates = ratesData.usd;
    
    // Get all available currencies with their info
    const currencies: Record<string, CurrencyInfo> = {};
    
    for (const [code, rate] of Object.entries(rates)) {
      const upperCode = code.toUpperCase();
      const info = currencySymbols[upperCode] || { 
        symbol: upperCode, 
        name: upperCode 
      };
      
      currencies[upperCode] = {
        code: upperCode,
        symbol: info.symbol,
        name: info.name,
        rate: typeof rate === 'number' ? rate : 1
      };
    }
    
    // Make sure USD is always included
    if (!currencies.USD) {
      currencies.USD = {
        code: 'USD',
        symbol: '$',
        name: 'US Dollar',
        rate: 1
      };
    }
    
    // Get the user's currency info
    const userCurrency = currencies[currencyCode] || currencies.USD;
    
    return NextResponse.json({
      ip,
      country_code: countryCode,
      country_name: countryName,
      currency: userCurrency,
      currencies: Object.values(currencies).sort((a, b) => a.code.localeCompare(b.code))
    });
  } catch (error) {
    console.error('Error fetching currency data:', error);
    
    // Return default values if there's an error
    return NextResponse.json({
      ip: '127.0.0.1',
      country_code: 'US',
      country_name: 'United States',
      currency: {
        code: 'USD',
        symbol: '$',
        name: 'US Dollar',
        rate: 1
      },
      currencies: [
        {
          code: 'USD',
          symbol: '$',
          name: 'US Dollar',
          rate: 1
        }
      ]
    });
  }
}
