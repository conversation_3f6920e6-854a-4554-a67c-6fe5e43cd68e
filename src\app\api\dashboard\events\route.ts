import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/session';

export const dynamic = 'force-dynamic';

// Helper function to add full image URLs
function addImageUrls(events: any[], request: NextRequest) {
  const protocol = request.headers.get('x-forwarded-proto') || 'http';
  const host = request.headers.get('host') || 'localhost:3000';
  const baseUrl = `${protocol}://${host}`;

  return events.map(event => {
    if (event.imagePath) {
      // Add both the original path and a full URL
      return {
        ...event,
        imageUrl: `${baseUrl}/api/images${event.imagePath}`
      };
    }
    return event;
  });
}

export async function GET(request: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Find events created by the user
    // Use select to explicitly specify which fields to include (excluding rejectionReason)
    const events = await db.event.findMany({
      where: {
        userId: user.id!,
      },
      select: {
        id: true,
        title: true,
        description: true,
        startDate: true,
        endDate: true,
        venue: true,
        location: true,
        category: true,
        eventType: true,
        status: true,
        imagePath: true,
        createdAt: true,
        updatedAt: true,
        userId: true,
        startTime: true,
        endTime: true,
        timeZone: true,
        // Exclude rejectionReason to avoid the error
      },
      orderBy: [
        { startDate: 'asc' },
      ],
    });

    // Add image URLs
    const eventsWithImages = addImageUrls(events, request);

    return NextResponse.json({ events: eventsWithImages });
  } catch (error) {
    console.error('Error fetching dashboard events:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
