import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/session';

export const dynamic = 'force-dynamic';

// Helper function to add full image URLs
function addImageUrls(events: any[], request: NextRequest) {
  const protocol = request.headers.get('x-forwarded-proto') || 'http';
  const host = request.headers.get('host') || 'localhost:3000';
  const baseUrl = `${protocol}://${host}`;

  return events.map(event => {
    if (event.imagePath) {
      // Add both the original path and a full URL
      return {
        ...event,
        imageUrl: `${baseUrl}/api/images${event.imagePath}`
      };
    }
    return event;
  });
}

export async function GET(request: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Find events created by the user that are featured
    const events = await db.event.findMany({
      where: {
        userId: user.id!,
        metadata: {
          path: ['isFeatured'],
          equals: 'true',
        },
      },
      include: {
        promotions: {
          where: {
            status: 'Active',
            type: 'Featured',
          },
          select: {
            id: true,
            type: true,
            startDate: true,
            endDate: true,
          },
        },
      },
      orderBy: [
        { startDate: 'asc' },
      ],
    });
    
    // Add image URLs
    const eventsWithImages = addImageUrls(events, request);
    
    // Format the response
    const formattedEvents = eventsWithImages.map(event => {
      // Extract metadata
      const metadata = event.metadata || {};
      const featuredUntil = metadata.featuredUntil ? new Date(metadata.featuredUntil) : null;
      
      // Get the active promotion
      const activePromotion = event.promotions && event.promotions.length > 0 
        ? event.promotions[0] 
        : null;
      
      return {
        id: event.id,
        title: event.title,
        startDate: event.startDate,
        endDate: event.endDate,
        venue: event.venue,
        location: event.location,
        imageUrl: event.imageUrl,
        featuredUntil: featuredUntil || (activePromotion ? activePromotion.endDate : null),
        promotionId: activePromotion ? activePromotion.id : null,
        promotionType: activePromotion ? activePromotion.type : 'Featured',
      };
    });
    
    return NextResponse.json({ events: formattedEvents });
  } catch (error) {
    console.error('Error fetching featured events:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
