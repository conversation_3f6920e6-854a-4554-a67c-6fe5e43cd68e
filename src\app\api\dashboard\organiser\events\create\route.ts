import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { EventStatus } from '@prisma/client';
import { validateEventData } from '@/lib/validation/event-validation';
import { mapToPrismaEventType } from '@/lib/utils';
import { EventFormData } from '@/types/events';
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    // Log headers for debugging
    console.log('Request headers:', Object.fromEntries(request.headers.entries()));
    const formData = await request.formData();

    // Log all form data entries for debugging
    console.log('Raw form data entries:');
    Array.from(formData.entries()).forEach(([key, value]) => {
      if (value instanceof File) {
        console.log(`${key}: [File] ${value.name} (${value.size} bytes)`);
      } else {
        // Try to detect if it's JSO<PERSON>
        if (typeof value === 'string' && (value.startsWith('{') || value.startsWith('['))) {
          try {
            const parsed = JSON.parse(value);
            console.log(`${key}: [JSON] ${JSON.stringify(parsed).substring(0, 100)}${JSON.stringify(parsed).length > 100 ? '...' : ''}`);
          } catch (e) {
            console.log(`${key}: ${value}`);
          }
        } else {
          console.log(`${key}: ${value}`);
        }
      }
    });

    const data = Object.fromEntries(formData.entries()) as unknown as EventFormData;

    // Parse JSON fields if they exist
    const ageRestriction = data.ageRestriction ?
      (typeof data.ageRestriction === 'string' ? JSON.parse(data.ageRestriction) : data.ageRestriction) :
      null;

    // Parse parking management data with detailed logging
    let parkingManagement = null;
    try {
      console.log('Raw parking management data:', data.parkingManagement);

      // Look for parking data in different formats
      // 1. Check if it's in the parkingManagement field
      if (data.parkingManagement) {
        if (typeof data.parkingManagement === 'string') {
          console.log('Parsing parking management from string');
          parkingManagement = JSON.parse(data.parkingManagement);
        } else {
          console.log('Using parking management as object');
          parkingManagement = data.parkingManagement;
        }
        console.log('Parsed parking management:', parkingManagement);
      } else {
        console.log('No parkingManagement field found, looking for alternative formats');

        // 2. Check if it's in a field called 'parking'
        if ((data as any).parking) {
          console.log('Found parking field');
          if (typeof (data as any).parking === 'string') {
            try {
              parkingManagement = JSON.parse((data as any).parking);
              console.log('Parsed parking field as JSON:', parkingManagement);
            } catch (e) {
              console.log('Parking field is not valid JSON:', (data as any).parking);
            }
          } else {
            parkingManagement = (data as any).parking;
            console.log('Using parking field as object:', parkingManagement);
          }
        }

        // 3. Check if individual parking fields exist
        const parkingFields: Record<string, any> = {};
        let hasAnyParkingField = false;

        // Check for fields with parking_ prefix
        Array.from(formData.entries()).forEach(([key, value]) => {
          if (typeof key === 'string' && key.startsWith('parking_')) {
            const fieldName = key.replace('parking_', '');
            parkingFields[fieldName] = value;
            hasAnyParkingField = true;
            console.log(`Found parking field with prefix: ${key}, value: ${value}`);
          }
        });

        // If we found any parking fields, use them
        if (hasAnyParkingField) {
          console.log('Using individual parking fields:', parkingFields);
          parkingManagement = parkingFields;
        }
      }
    } catch (error) {
      console.error('Error parsing parking management data:', error);
      parkingManagement = null;
    }

    const ticketTypes = data.ticketTypes ?
      (typeof data.ticketTypes === 'string' ? JSON.parse(data.ticketTypes) : data.ticketTypes) :
      [];

    const eventSettings = data.eventSettings ?
      (typeof data.eventSettings === 'string' ? JSON.parse(data.eventSettings) : data.eventSettings) :
      null;

    const seoSettings = data.seoSettings ?
      (typeof data.seoSettings === 'string' ? JSON.parse(data.seoSettings) : data.seoSettings) :
      null;

    const promoCodes = data.promoCodes ?
      (typeof data.promoCodes === 'string' ? JSON.parse(data.promoCodes) : data.promoCodes) :
      [];

    // Parse sponsors data with detailed logging
    let sponsors: any[] = [];
    try {
      console.log('All form data keys:', Object.keys(data));

      // IMPORTANT: First, check for rawSponsors field
      if ((data as any).rawSponsors) {
        console.log('Found rawSponsors field');
        const rawSponsors = (data as any).rawSponsors;

        if (typeof rawSponsors === 'string') {
          try {
            const parsed = JSON.parse(rawSponsors);
            if (Array.isArray(parsed)) {
              sponsors = parsed;
              console.log('Successfully parsed rawSponsors array from string');
            } else if (parsed && typeof parsed === 'object') {
              sponsors = [parsed];
              console.log('Successfully parsed single rawSponsor from string');
            }
          } catch (error) {
            console.error('Error parsing rawSponsors:', error);
          }
        } else if (Array.isArray(rawSponsors)) {
          sponsors = rawSponsors;
          console.log('Using rawSponsors array directly');
        } else if (rawSponsors && typeof rawSponsors === 'object') {
          sponsors = [rawSponsors];
          console.log('Using single rawSponsor object directly');
        }
      }

      // Then, check for a single sponsor
      if (sponsors.length === 0 && (data as any).hasSingleSponsor === 'true' && (data as any).singleSponsor) {
        console.log('Found single sponsor field');
        const singleSponsor = (data as any).singleSponsor;

        if (typeof singleSponsor === 'string') {
          try {
            const parsedSponsor = JSON.parse(singleSponsor);
            sponsors = [parsedSponsor];
            console.log('Successfully parsed single sponsor from string');
          } catch (error) {
            console.error('Error parsing single sponsor:', error);
          }
        } else if (typeof singleSponsor === 'object') {
          sponsors = [singleSponsor];
          console.log('Using single sponsor object directly');
        }
      }

      // Then, check for the dedicated sponsorsData field
      if (sponsors.length === 0 && (data as any).sponsorsData) {
        console.log('Found sponsorsData field');
        const sponsorsData = (data as any).sponsorsData;

        if (typeof sponsorsData === 'string') {
          try {
            sponsors = JSON.parse(sponsorsData);
            console.log('Successfully parsed sponsorsData from string');
          } catch (error) {
            console.error('Error parsing sponsorsData:', error);
          }
        } else if (Array.isArray(sponsorsData)) {
          sponsors = sponsorsData;
          console.log('Using sponsorsData array directly');
        }
      }

      // If no sponsors found yet, check the sponsors field
      if (sponsors.length === 0 && (data as any).sponsors) {
        console.log('Checking sponsors field');
        const sponsorsField = (data as any).sponsors;

        if (typeof sponsorsField === 'string') {
          try {
            const parsed = JSON.parse(sponsorsField);
            if (Array.isArray(parsed)) {
              sponsors = parsed;
              console.log('Successfully parsed sponsors array from string');
            } else if (parsed && typeof parsed === 'object') {
              // Handle single sponsor object
              sponsors = [parsed];
              console.log('Successfully parsed single sponsor from string');
            }
          } catch (error) {
            console.error('Error parsing sponsors field:', error);
          }
        } else if (Array.isArray(sponsorsField)) {
          sponsors = sponsorsField;
          console.log('Using sponsors array directly');
        } else if (sponsorsField && typeof sponsorsField === 'object') {
          // Handle single sponsor object
          sponsors = [sponsorsField];
          console.log('Using single sponsor object directly');
        }
      }

      // If still no sponsors, check for hasSponsors flag
      if (sponsors.length === 0 && (data as any).hasSponsors === 'true') {
        console.log('hasSponsors is true, checking individual sponsor fields');
        const sponsorCount = parseInt((data as any).sponsorsCount || '0', 10);

        if (sponsorCount > 0) {
          const individualSponsors = [];

          for (let i = 0; i < sponsorCount; i++) {
            const nameKey = `sponsor_${i}_name`;
            const tierKey = `sponsor_${i}_tier`;
            const logoKey = `sponsor_${i}_logo`;
            const websiteKey = `sponsor_${i}_website`;
            const amountKey = `sponsor_${i}_amount`;

            if ((data as any)[nameKey]) {
              console.log(`Found individual sponsor ${i}: ${(data as any)[nameKey]}`);

              individualSponsors.push({
                name: (data as any)[nameKey] || '',
                tier: (data as any)[tierKey] || 'BRONZE',
                logo: (data as any)[logoKey] || '',
                website: (data as any)[websiteKey] || '',
                amount: parseFloat((data as any)[amountKey] || '0'),
                id: crypto.randomUUID()
              });
            }
          }

          if (individualSponsors.length > 0) {
            sponsors = individualSponsors;
            console.log(`Constructed ${sponsors.length} sponsors from individual fields`);
          }
        }
      }

      // Final validation of sponsors array
      if (!Array.isArray(sponsors)) {
        console.error('Sponsors is not an array, converting to empty array');
        sponsors = [];
      }

      // Clean and validate each sponsor
      if (Array.isArray(sponsors) && sponsors.length > 0) {
        console.log('Cleaning and validating sponsors array with length:', sponsors.length);

        // Create a new array to avoid reference issues
        sponsors = sponsors.map((sponsor: any) => ({
          id: sponsor.id || crypto.randomUUID(),
          name: sponsor.name || '',
          logo: sponsor.logo || '',
          website: sponsor.website || '',
          tier: sponsor.tier || 'BRONZE',
          amount: typeof sponsor.amount === 'number' ? sponsor.amount : parseFloat(sponsor.amount?.toString() || '0')
        }));

        console.log('Final sponsors array after cleaning:', sponsors);
        console.log('Sponsors count after cleaning:', sponsors.length);
        console.log('Sponsors JSON after cleaning:', JSON.stringify(sponsors));
      } else {
        console.log('No sponsors to clean and validate');
      }
    } catch (error) {
      console.error('Error processing sponsors data:', error);
      sponsors = [];
    }

    // Log the form data for debugging
    console.log('Event creation form data:', {
      title: data.title,
      description: data.description?.substring(0, 50) + '...',
      eventType: data.eventType,
      category: data.category,
      startDate: data.startDate,
      endDate: data.endDate,
      startTime: data.startTime,
      endTime: data.endTime,
      location: data.location,
      venue: data.venue,
      venueCapacity: data.venueCapacity,
      virtualPlatform: data.virtualPlatform,
      isFree: data.isFree,
      hasTicketTypes: !!ticketTypes?.length,
      hasSponsors: !!sponsors?.length,
      hasParkingManagement: !!parkingManagement,
      hasAgeRestriction: !!ageRestriction,
    });

    // Log all form data keys for debugging
    console.log('All form data keys:', Object.keys(data));

    // Check for specific parking fields directly in the form data
    const parkingFields = ['totalSpaces', 'reservedSpaces', 'pricePerHour', 'isFree', 'reservationRequired'];
    console.log('Direct parking fields in form data:', parkingFields.map(field => ({
      field,
      exists: (data as any)[field] !== undefined,
      value: (data as any)[field]
    })));

    // Check if parking data might be in a different format
    const formDataEntries = Array.from(formData.entries());
    console.log('All form data entries:', formDataEntries.map(([key, value]) => {
      // For files, just log the name and type
      if (value instanceof File) {
        return { key, type: 'File', name: value.name, size: value.size };
      }
      // For other values, log the actual value
      return { key, value };
    }));

    // Pre-process data for validation
    const processedData = {
      ...data,
      ageRestriction: ageRestriction ? {
        ...ageRestriction,
        minAge: ageRestriction.minAge === '' ? null : ageRestriction.minAge,
        maxAge: ageRestriction.maxAge === '' ? null : ageRestriction.maxAge,
      } : null,
      parkingManagement: parkingManagement ? {
        ...parkingManagement,
        totalSpaces: parkingManagement.totalSpaces === '' ? null : parkingManagement.totalSpaces,
        reservedSpaces: parkingManagement.reservedSpaces === '' ? null : parkingManagement.reservedSpaces,
        pricePerHour: parkingManagement.pricePerHour === '' ? null : parkingManagement.pricePerHour,
      } : null,
      ticketTypes,
      eventSettings,
      seoSettings,
      promoCodes,
      sponsors
    };

    console.log('Processed data for validation:', {
      ageRestriction: processedData.ageRestriction,
      parkingManagement: processedData.parkingManagement
    });

    // Validate the event data
    const { isValid, errors } = validateEventData(processedData);

    if (!isValid) {
      // Ensure errors is a properly formatted object
      const formattedErrors = errors || { general: 'Validation failed' };
      console.log('Validation errors:', formattedErrors);

      // Create a response with CORS headers
      const response = NextResponse.json({
        error: 'Validation failed',
        errors: formattedErrors
      }, { status: 400 });

      // Add CORS headers
      response.headers.set('Access-Control-Allow-Origin', '*');
      response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key, Cache-Control');

      return response;
    }

    // Check if an event with the same title already exists
    const existingEvent = await db.event.findFirst({
      where: {
        title: data.title,
      },
    });

    if (existingEvent) {
      return NextResponse.json({
        error: 'An event with this title already exists. Please choose a different title.'
      }, { status: 400 });
    }

    // Get the current user
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if the user is an organizer
    if (user.role !== 'ORGANIZER') {
      return NextResponse.json({ error: 'Only organizers can create events' }, { status: 403 });
    }

    // Check the organizer's verification status
    const verification = await db.organizerVerification.findUnique({
      where: { userId: user.id },
    });

    // Set the event status based on verification status and draft flag
    let eventStatus = 'Draft';

    // Check if the user explicitly wants to save as draft
    const saveAsDraft = formData.get('saveAsDraft') === 'true';
    console.log('Save as draft:', saveAsDraft);

    // If the organizer is verified and not saving as draft, they can publish events
    // Otherwise, events will be in draft mode
    if (verification?.status === 'APPROVED' && !saveAsDraft) {
      eventStatus = 'Published';
    }

    console.log('Organizer verification status:', verification?.status || 'NOT_SUBMITTED');
    console.log('Event will be created with status:', eventStatus);

    // Use the user ID from the session
    const effectiveUser = { id: user.id };

    // Handle image upload if provided
    let imagePath = '';
    const image = formData.get('image') as File;
    if (image && image.size > 0) {
      try {
        // Validate file type
        const fileExtension = path.extname(image.name).toLowerCase();
        const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];

        if (!allowedExtensions.includes(fileExtension)) {
          throw new Error(`Invalid file type. Allowed types: ${allowedExtensions.join(', ')}`);
        }

        // Validate file size (max 2MB)
        const maxSize = 2 * 1024 * 1024; // 2MB
        if (image.size > maxSize) {
          throw new Error('File size exceeds the 2MB limit');
        }

        // Create unique filename
        const uniqueId = uuidv4();
        const sanitizedFileName = image.name.replace(/[^a-zA-Z0-9.-]/g, '_');
        const filename = `${uniqueId}-${sanitizedFileName}`;

        // Define upload directory and path
        const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'events');

        // Ensure directory exists
        await mkdir(uploadDir, { recursive: true });

        // Convert file to buffer
        const arrayBuffer = await image.arrayBuffer();
        const buffer = new Uint8Array(arrayBuffer);

        // Write file to disk
        const filePath = path.join(uploadDir, filename);
        await writeFile(filePath, buffer);

        // Set the image path relative to the public directory
        imagePath = `/uploads/events/${filename}`;
      } catch (uploadError) {
        console.error('Error uploading image:', uploadError);
        return NextResponse.json({ error: 'Failed to upload image' }, { status: 500 });
      }
    }

    // Create age restriction record if provided and has meaningful values
    let createdAgeRestriction = null;
    if (ageRestriction &&
        ((ageRestriction.minAge && parseInt(String(ageRestriction.minAge), 10) > 0) ||
         (ageRestriction.maxAge && parseInt(String(ageRestriction.maxAge), 10) > 0) ||
         (ageRestriction.ageGroups && String(ageRestriction.ageGroups).trim() !== '') ||
         (ageRestriction.description && String(ageRestriction.description).trim() !== ''))) {
      try {
        console.log('Creating age restriction record with data:', {
          minAge: parseInt(String(ageRestriction.minAge || '0'), 10) || null,
          maxAge: parseInt(String(ageRestriction.maxAge || '0'), 10) || null,
          ageGroups: String(ageRestriction.ageGroups || '').trim() || null,
          description: String(ageRestriction.description || '').trim() || null
        });

        createdAgeRestriction = await db.ageRestriction.create({
          data: {
            minAge: parseInt(String(ageRestriction.minAge || '0'), 10) > 0 ? parseInt(String(ageRestriction.minAge), 10) : null,
            maxAge: parseInt(String(ageRestriction.maxAge || '0'), 10) > 0 ? parseInt(String(ageRestriction.maxAge), 10) : null,
            ageGroups: String(ageRestriction.ageGroups || '').trim() || null,
            description: String(ageRestriction.description || '').trim() || null,
          },
        });

        console.log('Successfully created age restriction record with ID:', createdAgeRestriction.id);
      } catch (ageRestrictionError) {
        console.error('Error creating age restriction record:', ageRestrictionError);
        // Continue with event creation even if age restriction creation fails
        console.log('Continuing with event creation despite age restriction error');
        createdAgeRestriction = null;
      }
    } else {
      console.log('No meaningful age restriction data provided, skipping creation');
    }

    // Create event in database with enhanced fields
    // Convert custom EventType to Prisma's EventType
    const prismaEventType = mapToPrismaEventType(data.eventType);

    const event = await db.event.create({
      data: {
        title: data.title,
        description: data.description,
        eventType: prismaEventType,
        category: data.category,
        startDate: new Date(`${data.startDate}T${data.startTime}`),
        endDate: new Date(`${data.endDate}T${data.endTime}`),
        location: data.location || '',
        venue: data.venue || '',
        startTime: data.startTime,
        endTime: data.endTime,
        imagePath,
        userId: effectiveUser.id,
        status: eventStatus as EventStatus,
        ageRestrictionId: createdAgeRestriction?.id,
        timeZone: data.timeZone || 'UTC+2',
        teamId: data.teamId || null
      },
    });

    // Create parking management record
    // Always create a parking record, even if totalSpaces is 0
    try {
      console.log('Creating parking management record for event:', event.id);

      // Parse and validate parking management data
      let totalSpaces = 0;
      let reservedSpaces = 0;
      let pricePerHour = 0;
      let isFree = false;
      let reservationRequired = false;
      let description = 'No parking information provided';

      // Try to get parking data from multiple possible sources
      if (parkingManagement) {
        console.log('Using parkingManagement object');
        // Ensure numeric values are properly parsed
        totalSpaces = parseInt(String(parkingManagement.totalSpaces || '0'), 10) || 0;
        reservedSpaces = parseInt(String(parkingManagement.reservedSpaces || '0'), 10) || 0;
        pricePerHour = parseFloat(String(parkingManagement.pricePerHour || '0')) || 0;

        // Ensure boolean values are properly parsed
        isFree = parkingManagement.isFree === true || parkingManagement.isFree === 'true';
        reservationRequired = parkingManagement.reservationRequired === true || parkingManagement.reservationRequired === 'true';

        // Ensure string values are properly handled
        if (parkingManagement.description) {
          description = String(parkingManagement.description);
        }
      } else {
        // Check if parking fields are directly in the form data
        console.log('Checking for direct parking fields in form data');

        // Check for totalSpaces
        if ((data as any).totalSpaces !== undefined) {
          totalSpaces = parseInt(String((data as any).totalSpaces || '0'), 10) || 0;
          console.log('Found totalSpaces directly in form data:', totalSpaces);
        }

        // Check for reservedSpaces
        if ((data as any).reservedSpaces !== undefined) {
          reservedSpaces = parseInt(String((data as any).reservedSpaces || '0'), 10) || 0;
          console.log('Found reservedSpaces directly in form data:', reservedSpaces);
        }

        // Check for pricePerHour
        if ((data as any).pricePerHour !== undefined) {
          pricePerHour = parseFloat(String((data as any).pricePerHour || '0')) || 0;
          console.log('Found pricePerHour directly in form data:', pricePerHour);
        }

        // Check for isFree
        if ((data as any).isFree !== undefined) {
          const isFreeValue = (data as any).isFree;
          isFree = isFreeValue === true ||
                  (typeof isFreeValue === 'string' && isFreeValue.toLowerCase() === 'true');
          console.log('Found isFree directly in form data:', isFree);
        }

        // Check for reservationRequired
        if ((data as any).reservationRequired !== undefined) {
          const reqValue = (data as any).reservationRequired;
          reservationRequired = reqValue === true ||
                               (typeof reqValue === 'string' && reqValue.toLowerCase() === 'true');
          console.log('Found reservationRequired directly in form data:', reservationRequired);
        }

        // Check for description
        if ((data as any).parkingDescription !== undefined) {
          description = String((data as any).parkingDescription || '');
          console.log('Found parkingDescription directly in form data:', description);
        }

        // Check for individual form fields with parking_ prefix
        formDataEntries.forEach(([key, value]) => {
          if (typeof key === 'string' && key.startsWith('parking_')) {
            const fieldName = key.replace('parking_', '');
            console.log(`Found parking field with prefix: ${key}, value: ${value}`);

            if (fieldName === 'totalSpaces') {
              totalSpaces = parseInt(String(value || '0'), 10) || 0;
            } else if (fieldName === 'reservedSpaces') {
              reservedSpaces = parseInt(String(value || '0'), 10) || 0;
            } else if (fieldName === 'pricePerHour') {
              pricePerHour = parseFloat(String(value || '0')) || 0;
            } else if (fieldName === 'isFree') {
              isFree = String(value).toLowerCase() === 'true';
            } else if (fieldName === 'reservationRequired') {
              reservationRequired = String(value).toLowerCase() === 'true';
            } else if (fieldName === 'description') {
              description = String(value || '');
            }
          }
        });
      }

      // Log the parsed values for debugging
      console.log('Parking data to be inserted:', {
        totalSpaces,
        reservedSpaces,
        pricePerHour,
        isFree,
        reservationRequired,
        description
      });

      // Create the parking management record
      await db.parkingManagement.create({
        data: {
          eventId: event.id,
          totalSpaces,
          reservedSpaces,
          pricePerHour,
          isFree,
          reservationRequired,
          description,
        },
      });

      console.log('Successfully created parking management record');
    } catch (parkingError) {
      console.error('Error creating parking management record:', parkingError);
      // Continue with event creation even if parking management creation fails
      console.log('Continuing with event creation despite parking management error');
    }

    // Import the OrderStatus and TicketType enums from Prisma
    const { OrderStatus, TicketType } = await import('@prisma/client');

    // Create a default order for the event tickets
    const defaultOrder = await db.order.create({
      data: {
        userId: user.id!,
        eventId: event.id,
        status: OrderStatus.Pending, // Use the enum value directly
        totalPrice: 0, // This is just a placeholder for ticket templates
        pricePaid: 0,
        customerName: user.name || 'Organizer',
        customerEmail: user.email || '<EMAIL>',
        customerPhone: '+1234567890', // Placeholder phone number
      },
    });

    console.log('Created default order:', defaultOrder.id);

    // Helper function to map ticket name to TicketType enum
    const mapToTicketType = (name: string) => {
      const normalizedName = name.toUpperCase().replace(/\s+/g, '_');
      switch (normalizedName) {
        case 'REGULAR':
          return TicketType.REGULAR;
        case 'VIP':
          return TicketType.VIP;
        case 'VVIP':
          return TicketType.VVIP;
        case 'EARLY_BIRD':
        case 'EARLYBIRD':
        case 'EARLY-BIRD':
          return TicketType.EARLY_BIRD;
        case 'GROUP':
          return TicketType.GROUP;
        default:
          console.warn(`Unknown ticket type: ${name}, defaulting to REGULAR`);
          return TicketType.REGULAR;
      }
    };

    // Create ticket types if provided
    try {
      if (ticketTypes && ticketTypes.length > 0) {
        for (const ticket of ticketTypes) {
          try {
            const ticketType = mapToTicketType(ticket.name);
            console.log(`Creating ticket of type ${ticketType} for event ${event.id}`);

            await db.ticket.create({
              data: {
                eventId: event.id,
                type: ticketType,
                price: ticket.price,
                quantity: ticket.quantity,
                description: ticket.description || '',
                saleStartTime: ticket.startSaleDate ? new Date(ticket.startSaleDate).toISOString() : new Date(`${data.startDate}T${data.startTime}`).toISOString(),
                saleEndTime: ticket.endSaleDate ? new Date(ticket.endSaleDate).toISOString() : new Date(`${data.endDate}T${data.endTime}`).toISOString(),
                specialGuestType: ticket.specialGuestType || 'None',
                specialGuestName: ticket.specialGuestName || '',
                orderId: defaultOrder.id,
                email: user.email || '<EMAIL>',
                totalPrice: 0, // Placeholder for ticket template
                totalSeats: ticket.quantity,
                regularSeats: ticketType === TicketType.REGULAR ? ticket.quantity : 0,
                vipSeats: ticketType === TicketType.VIP ? ticket.quantity : 0,
                vvipSeats: ticketType === TicketType.VVIP ? ticket.quantity : 0,
                regularPrice: ticketType === TicketType.REGULAR ? ticket.price : 0,
                vipPrice: ticketType === TicketType.VIP ? ticket.price : 0,
                vvipPrice: ticketType === TicketType.VVIP ? ticket.price : 0,
                qrCodeData: `event-${event.id}-ticket-template-${ticketType}`,
                userId: user.id!,
              },
            });
            console.log(`Successfully created ticket of type ${ticketType}`);
          } catch (ticketError) {
            console.error(`Error creating ticket of type ${ticket.name}:`, ticketError);
            throw new Error(`Failed to create ticket: ${ticketError instanceof Error ? ticketError.message : 'Unknown error'}`);
          }
        }
      } else {
        // Create default tickets if no ticket types provided
        if (data.regularSeats && data.regularSeats > 0) {
          try {
            console.log(`Creating REGULAR ticket for event ${event.id}`);

            await db.ticket.create({
              data: {
                eventId: event.id,
                type: TicketType.REGULAR,
                price: data.regularPrice || 0,
                quantity: data.regularSeats,
                description: 'Regular admission ticket',
                saleStartTime: new Date(`${data.startDate}T${data.startTime}`).toISOString(),
                saleEndTime: new Date(`${data.endDate}T${data.endTime}`).toISOString(),
                orderId: defaultOrder.id,
                email: user.email || '<EMAIL>',
                totalPrice: 0, // Placeholder for ticket template
                totalSeats: data.regularSeats,
                regularSeats: data.regularSeats,
                vipSeats: 0,
                vvipSeats: 0,
                regularPrice: data.regularPrice || 0,
                vipPrice: 0,
                qrCodeData: `event-${event.id}-ticket-template-regular`,
                userId: user.id!,
                specialGuestType: 'None',
                specialGuestName: '',
              },
            });
            console.log(`Successfully created REGULAR ticket`);
          } catch (regularTicketError) {
            console.error('Error creating REGULAR ticket:', regularTicketError);
            throw new Error(`Failed to create REGULAR ticket: ${regularTicketError instanceof Error ? regularTicketError.message : 'Unknown error'}`);
          }
        }

        if (data.vipSeats && data.vipSeats > 0) {
          try {
            console.log(`Creating VIP ticket for event ${event.id}`);

            await db.ticket.create({
              data: {
                eventId: event.id,
                type: TicketType.VIP,
                price: data.vipPrice || 0,
                quantity: data.vipSeats,
                description: 'VIP admission ticket',
                saleStartTime: new Date(`${data.startDate}T${data.startTime}`).toISOString(),
                saleEndTime: new Date(`${data.endDate}T${data.endTime}`).toISOString(),
                orderId: defaultOrder.id,
                email: user.email || '<EMAIL>',
                totalPrice: 0, // Placeholder for ticket template
                totalSeats: data.vipSeats,
                regularSeats: 0,
                vipSeats: data.vipSeats,
                vvipSeats: 0,
                regularPrice: 0,
                vipPrice: data.vipPrice || 0,
                qrCodeData: `event-${event.id}-ticket-template-vip`,
                userId: user.id!,
                specialGuestType: 'None',
                specialGuestName: '',
              },
            });
            console.log(`Successfully created VIP ticket`);
          } catch (vipTicketError) {
            console.error('Error creating VIP ticket:', vipTicketError);
            throw new Error(`Failed to create VIP ticket: ${vipTicketError instanceof Error ? vipTicketError.message : 'Unknown error'}`);
          }
        }
      }
    } catch (ticketsError) {
      console.error('Error in ticket creation process:', ticketsError);
      // Continue with event creation even if tickets fail
      // The event will be created without tickets, which can be added later
      console.log('Continuing with event creation despite ticket errors');
    }

    // Store event settings and venue capacity in the event record
    if (eventSettings || data.venueCapacity) {
      console.log('Storing event settings and/or venue capacity in metadata');

      // Get current metadata
      const currentEvent = await db.event.findUnique({
        where: { id: event.id },
        select: { metadata: true },
      });

      // Prepare new metadata by combining with existing
      let currentMetadata: Record<string, any> = {};
      if (currentEvent?.metadata) {
        try {
          // Handle both string and object metadata formats
          if (typeof currentEvent.metadata === 'string') {
            currentMetadata = JSON.parse(currentEvent.metadata);
          } else {
            currentMetadata = currentEvent.metadata as any;
          }
        } catch (e) {
          console.error('Error parsing existing metadata:', e);
          currentMetadata = {};
        }
      }

      // Create updated metadata object
      const updatedMetadata: Record<string, any> = { ...currentMetadata };

      // Add event settings if provided
      if (eventSettings) {
        updatedMetadata.eventSettings = {
          isPublic: eventSettings.isPublic,
          requiresRegistration: eventSettings.requiresRegistration,
          enableWaitlist: eventSettings.enableWaitlist,
          waitlistSize: eventSettings.waitlistSize || 0,
          customFields: eventSettings.customFields || [],
        };
      }

      // Add venue capacity if provided
      if (data.venueCapacity) {
        console.log('Venue capacity provided:', data.venueCapacity);
        updatedMetadata.venueCapacity = data.venueCapacity;
      }

      // Add venue coordinates if provided
      if (data.venueCoordinates) {
        console.log('Venue coordinates provided:', data.venueCoordinates);
        updatedMetadata.venueCoordinates = data.venueCoordinates;
      }

      // Update the event with metadata
      await db.event.update({
        where: { id: event.id },
        data: {
          metadata: updatedMetadata,
        },
      });
      console.log('Updated event with metadata including:', Object.keys(updatedMetadata).join(', '));
    }

    // Store SEO settings in the dedicated SeoSettings table
    if (seoSettings) {
      try {
        console.log('Creating SEO settings record for event:', event.id);
        // Use raw SQL query as a workaround for Prisma client issues
        await db.$executeRaw`
          INSERT INTO "SeoSettings" (id, title, description, keywords, "eventId", "createdAt", "updatedAt")
          VALUES (
            ${crypto.randomUUID()},
            ${seoSettings.title || ''},
            ${seoSettings.description || ''},
            ${seoSettings.keywords || []},
            ${event.id},
            NOW(),
            NOW()
          )
        `;
        /* Commented out due to Prisma client issues
        await db.seoSettings.create({
          data: {
            title: seoSettings.title || '',
            description: seoSettings.description || '',
            keywords: seoSettings.keywords || [],
            eventId: event.id
          }
        });
        */
        console.log('Successfully created SEO settings record');
      } catch (seoError) {
        console.error('Error creating SEO settings record:', seoError);
        // Continue with event creation even if SEO settings creation fails
        console.log('Continuing with event creation despite SEO settings error');
      }
    }

    // Store Social settings in the dedicated SocialSettings table
    if (data.socialSettings) {
      try {
        const socialSettings = typeof data.socialSettings === 'string'
          ? JSON.parse(data.socialSettings)
          : data.socialSettings;

        console.log('Creating Social settings record for event:', event.id);
        // Use raw SQL query as a workaround for Prisma client issues
        await db.$executeRaw`
          INSERT INTO "SocialSettings" (id, "facebookTitle", "facebookDescription", "twitterTitle", "twitterDescription", "ogImage", "eventId", "createdAt", "updatedAt")
          VALUES (
            ${crypto.randomUUID()},
            ${socialSettings.facebookTitle || ''},
            ${socialSettings.facebookDescription || ''},
            ${socialSettings.twitterTitle || ''},
            ${socialSettings.twitterDescription || ''},
            ${socialSettings.ogImage || ''},
            ${event.id},
            NOW(),
            NOW()
          )
        `;
        /* Commented out due to Prisma client issues
        await db.socialSettings.create({
          data: {
            facebookTitle: socialSettings.facebookTitle || '',
            facebookDescription: socialSettings.facebookDescription || '',
            twitterTitle: socialSettings.twitterTitle || '',
            twitterDescription: socialSettings.twitterDescription || '',
            ogImage: socialSettings.ogImage || '',
            eventId: event.id
          }
        });
        */
        console.log('Successfully created Social settings record');
      } catch (socialError) {
        console.error('Error creating Social settings record:', socialError);
        // Continue with event creation even if Social settings creation fails
        console.log('Continuing with event creation despite Social settings error');
      }
    }

    // Store promo codes in the event record if provided
    if (promoCodes && promoCodes.length > 0) {
      console.log('Promo codes provided, storing in event metadata');

      // Get current metadata
      const currentEvent = await db.event.findUnique({
        where: { id: event.id },
        select: { metadata: true },
      });

      // Prepare new metadata by combining with existing
      let currentMetadata: Record<string, any> = {};
      if (currentEvent?.metadata) {
        try {
          // Handle both string and object metadata formats
          if (typeof currentEvent.metadata === 'string') {
            currentMetadata = JSON.parse(currentEvent.metadata);
          } else {
            currentMetadata = currentEvent.metadata as any;
          }
        } catch (e) {
          console.error('Error parsing existing metadata:', e);
          currentMetadata = {};
        }
      }

      const updatedMetadata: Record<string, any> = {
        ...currentMetadata,
        promoCodes: promoCodes.map((promo: { code: string; discountType?: string; discountValue?: number | string; maxUses?: number | string; expiryDate?: string; description?: string }) => ({
          code: promo.code,
          discountType: promo.discountType,
          discountValue: promo.discountValue,
          maxUses: promo.maxUses || 0,
          expiryDate: promo.expiryDate || null,
          description: promo.description || '',
        })),
      };

      // Update the event with promo codes as metadata
      await db.event.update({
        where: { id: event.id },
        data: {
          metadata: updatedMetadata,
        },
      });
      console.log('Updated event metadata with promo codes');
    }

    // Create sponsors if provided
    // Ensure sponsors is an array
    if (!Array.isArray(sponsors)) {
      console.error('Sponsors is still not an array after parsing, setting to empty array');
      sponsors = [];
    }

    console.log('Final sponsors data for processing:', sponsors);
    console.log('Final sponsors length:', sponsors.length);
    console.log('Final sponsors data type:', typeof sponsors);
    console.log('Final sponsors is array:', Array.isArray(sponsors));

    // Double check if sponsors is empty but hasSponsors is true
    if (sponsors.length === 0 && (data as any).hasSponsors === 'true') {
      console.log('hasSponsors is true but sponsors array is empty, checking for individual sponsor fields again');
      const sponsorCount = parseInt((data as any).sponsorsCount || '0', 10);

      if (sponsorCount > 0) {
        console.log(`Found sponsorCount: ${sponsorCount}, reconstructing sponsors array`);
        // Try one more time to construct sponsors from individual fields
        for (let i = 0; i < sponsorCount; i++) {
          const nameKey = `sponsor_${i}_name`;
          if ((data as any)[nameKey]) {
            console.log(`Found sponsor name: ${(data as any)[nameKey]}`);
            sponsors.push({
              name: (data as any)[nameKey] || '',
              tier: (data as any)[`sponsor_${i}_tier`] || 'BRONZE',
              logo: (data as any)[`sponsor_${i}_logo`] || '',
              website: (data as any)[`sponsor_${i}_website`] || '',
              amount: parseFloat((data as any)[`sponsor_${i}_amount`] || '0'),
              id: crypto.randomUUID()
            });
          }
        }
        console.log('Reconstructed sponsors array:', sponsors);
        console.log('Reconstructed sponsors length:', sponsors.length);
      }
    }

    if (sponsors && sponsors.length > 0) {
      try {
        console.log('Creating sponsors for event:', event.id);
        console.log('Number of sponsors to create:', sponsors.length);
        console.log('Sponsors data:', sponsors.map((s: any) => ({ name: s.name, tier: s.tier })));

        // Import the SponsorshipTier enum from Prisma
        const { SponsorshipTier } = await import('@prisma/client');

        // Create an array to hold all sponsor data for batch creation
        const sponsorsToCreate = [];

        // Process each sponsor
        for (const sponsor of sponsors) {
          try {
            // Map string tier to SponsorshipTier enum
            let sponsorshipTier;
            try {
              // Check if the tier is a valid SponsorshipTier enum value
              if (Object.values(SponsorshipTier).includes(sponsor.tier as any)) {
                sponsorshipTier = sponsor.tier;
              } else {
                // Try to normalize the tier string to match enum format
                const normalizedTier = sponsor.tier?.toString().toUpperCase();
                if (normalizedTier && Object.values(SponsorshipTier).includes(normalizedTier as any)) {
                  sponsorshipTier = normalizedTier;
                } else {
                  // Default to BRONZE if invalid tier
                  sponsorshipTier = SponsorshipTier.BRONZE;
                  console.log(`Invalid sponsor tier: ${sponsor.tier}, defaulting to BRONZE`);
                }
              }
            } catch (error) {
              // Default to BRONZE if any error occurs
              sponsorshipTier = SponsorshipTier.BRONZE;
              console.log(`Error processing sponsor tier: ${error}, defaulting to BRONZE`);
            }

            // Validate sponsor data
            if (!sponsor.name || sponsor.name.trim() === '') {
              console.warn('Skipping sponsor with empty name');
              continue;
            }

            // Parse and validate sponsor data
            const sponsorData = {
              name: sponsor.name || '',
              logo: sponsor.logo || '',
              website: sponsor.website || '',
              tier: sponsorshipTier as any,
              amount: parseFloat(sponsor.amount?.toString() || '0'),
              eventId: event.id
            };

            console.log(`Preparing sponsor: ${sponsorData.name}, tier: ${sponsorData.tier}`);

            // Add to the array of sponsors to create
            sponsorsToCreate.push(sponsorData);
          } catch (sponsorError) {
            console.error(`Error processing sponsor ${sponsor.name}:`, sponsorError);
            // Continue with other sponsors even if one fails
          }
        }

        // Create all sponsors in a batch
        if (sponsorsToCreate.length > 0) {
          console.log(`Creating ${sponsorsToCreate.length} sponsors in batch`);

          // Create each sponsor individually to ensure all are created
          for (const sponsorData of sponsorsToCreate) {
            try {
              await db.sponsor.create({
                data: sponsorData
              });
              console.log(`Successfully created sponsor: ${sponsorData.name}`);
            } catch (createError) {
              console.error(`Error creating sponsor ${sponsorData.name}:`, createError);
            }
          }

          console.log('Successfully created all sponsors');
        } else {
          console.log('No valid sponsors to create');
        }
      } catch (sponsorsError) {
        console.error('Error creating sponsors:', sponsorsError);
        // Continue with event creation even if sponsors creation fails
        console.log('Continuing with event creation despite sponsors error');
      }
    }

    // Create initial analytics entry
    try {
      console.log('Creating initial analytics entry for event:', event.id);
      await db.analyticsEntry.create({
        data: {
          eventId: event.id,
          date: new Date(),
          ticketsSold: 0,
          ticketsUnsold: 0,
          refunds: 0,
          newCustomers: 0,
          existingCustomers: 0,
          engagements: 0,
          revenue: 0,
          costs: 0,
          profit: 0
        },
      });
      console.log('Successfully created analytics entry');
    } catch (analyticsError) {
      console.error('Error creating analytics entry:', analyticsError);
      // Continue with event creation even if analytics entry fails
      console.log('Continuing with event creation despite analytics entry error');
    }

    // Add verification status message to the response
    let verificationMessage = '';
    if (!verification || verification.status !== 'APPROVED') {
      verificationMessage = 'Your event has been created in draft mode. To publish it, you need to complete organizer verification.';
    }

    return NextResponse.json({
      success: true,
      message: 'Event created successfully',
      verificationMessage,
      event: {
        id: event.id,
        title: event.title,
        status: event.status,
        venueCapacity: data.venueCapacity || null,
      }
    });
  } catch (error) {
    console.error('Error creating event:', error);

    // Provide more detailed error information
    let errorMessage = 'Failed to create event';
    let errorDetails = error instanceof Error ? error.message : 'Unknown error';
    let statusCode = 500;
    let errors = {};

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('Unique constraint') || error.message.includes('duplicate key value')) {
        errorMessage = 'An event with this title already exists. Please choose a different title.';
        statusCode = 400;
      } else if (error.message.includes('Foreign key constraint')) {
        errorMessage = 'Invalid reference to another entity';
        statusCode = 400;
      } else if (error.message.includes('validation')) {
        errorMessage = 'Validation error';
        statusCode = 400;
      } else if (error.message.includes('Intersection results could not be merged')) {
        errorMessage = 'Data validation error';
        statusCode = 400;
        errors = {
          general: 'There was an issue with the form data. Please check all fields and try again.'
        };

        // Log additional debugging information
        console.error('Intersection error details:', {
          message: error.message,
          stack: error.stack
        });
      }

      // Log the full error message for debugging
      console.error('Detailed error message:', error.message);
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: errorDetails,
        errors: Object.keys(errors).length > 0 ? errors : undefined
      },
      { status: statusCode }
    );
  }
}

