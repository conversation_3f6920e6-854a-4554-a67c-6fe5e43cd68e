import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { Prisma } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    console.log('API: Fetching attendees');

    const user = await currentUser();
    console.log('API: User data:', user ? { id: user.id, role: user.role } : 'No user');

    if (!user?.id) {
      console.log('API: Unauthorized - No user ID');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get URL parameters for filtering and pagination
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || '';
    const eventId = url.searchParams.get('eventId') || '';
    const sortBy = url.searchParams.get('sortBy') || 'createdAt';
    const sortOrder = url.searchParams.get('sortOrder') || 'desc';

    console.log('API: Query parameters:', { page, limit, search, eventId, sortBy, sortOrder });

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get events for this organizer
    console.log('API: Finding events for user ID:', user.id);
    const events = await db.event.findMany({
      where: { userId: user.id },
      select: { id: true }
    });

    console.log('API: Found events:', events.length);
    const eventIds = events.map(event => event.id);

    // Build where clause for orders
    const where: Prisma.OrderWhereInput = {
      event: {
        id: eventId ? eventId : { in: eventIds },
        userId: user.id!
      },
      ...(search ? {
        OR: [
          { customerName: { contains: search, mode: Prisma.QueryMode.insensitive } },
          { customerEmail: { contains: search, mode: Prisma.QueryMode.insensitive } },
          { id: { contains: search, mode: Prisma.QueryMode.insensitive } }
        ]
      } : {})
    };

    console.log('API: Query where clause:', JSON.stringify(where));

    // Fetch orders with pagination
    console.log('API: Fetching orders...');
    const [orders, total] = await Promise.all([
      db.order.findMany({
        where,
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit,
        include: {
          event: {
            select: {
              id: true,
              title: true,
              startDate: true,
              imagePath: true
            }
          },
          tickets: true
        }
      }),
      db.order.count({ where })
    ]);

    console.log('API: Found orders:', orders.length, 'Total:', total);

    // Format attendee data
    const attendees = orders.map(order => ({
      id: order.id,
      name: order.customerName,
      email: order.customerEmail,
      phone: order.customerPhone || 'N/A',
      event: {
        id: order.event.id,
        title: order.event.title,
        date: order.event.startDate,
        imagePath: order.event.imagePath
      },
      tickets: order.tickets.map(ticket => ({
        id: ticket.id,
        type: ticket.type,
        quantity: ticket.quantity,
        price: ticket.price
      })),
      totalSpent: order.tickets.reduce((sum, ticket) => sum + (ticket.price * ticket.quantity), 0),
      purchaseDate: order.createdAt,
      status: order.status
    }));

    // Get event options for filter dropdown
    console.log('API: Fetching event options...');
    const eventOptions = await db.event.findMany({
      where: {
        userId: user.id!,
        id: { in: eventIds }
      },
      select: {
        id: true,
        title: true,
        _count: {
          select: { orders: true }
        }
      },
      orderBy: { startDate: 'desc' }
    });

    console.log('API: Found event options:', eventOptions.length);

    const response = {
      attendees,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      },
      filters: {
        events: eventOptions.map(event => ({
          id: event.id,
          title: event.title,
          attendeeCount: event._count.orders
        }))
      }
    };

    console.log('API: Sending response with', attendees.length, 'attendees');
    return NextResponse.json(response);
  } catch (error) {
    console.error('API Error fetching attendees:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Failed to fetch attendees', details: errorMessage },
      { status: 500 }
    );
  }
}
