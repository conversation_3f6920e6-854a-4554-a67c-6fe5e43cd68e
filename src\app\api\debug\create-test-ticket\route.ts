import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export const dynamic = 'force-dynamic';

/**
 * POST /api/debug/create-test-ticket
 * Create a test ticket for an event
 */
export async function POST(request: NextRequest) {
  try {
    const user = await currentUser();
    
    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const body = await request.json();
    const { eventId } = body;
    
    if (!eventId) {
      return NextResponse.json(
        { error: 'Event ID is required' },
        { status: 400 }
      );
    }
    
    // Check if the event exists
    const event = await db.event.findUnique({
      where: { id: eventId },
      select: { id: true, title: true, userId: true }
    });
    
    if (!event) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }
    
    // Create a test order
    const order = await db.order.create({
      data: {
        userId: user.id!,
        eventId: event.id,
        pricePaid: 99.99,
        totalPrice: 99.99,
        customerEmail: user.email || '<EMAIL>',
        customerName: user.name || 'Test User',
        customerPhone: '+1234567890',
        status: 'Completed',
      }
    });
    
    // Create a test ticket
    const ticket = await db.ticket.create({
      data: {
        eventId: event.id,
        userId: user.id!,
        orderId: order.id,
        type: 'REGULAR',
        price: 99.99,
        email: user.email || '<EMAIL>',
        saleStartTime: new Date().toISOString(),
        saleEndTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
        quantity: 1,
        totalPrice: 99,
        regularSeats: 1,
        vipSeats: 0,
        vvipSeats: 0,
        totalSeats: 1,
        regularPrice: 99.99,
        vipPrice: 149.99,
        specialGuestType: '',
        specialGuestName: '',
        qrCodeData: `ticket-${Math.random().toString(36).substring(2, 10)}`,
        isAvailable: true,
        description: 'Test ticket for debugging',
        Order: {
          connect: {
            id: order.id
          }
        }
      }
    });
    
    return NextResponse.json({
      success: true,
      message: 'Test ticket created successfully',
      ticket,
      order
    });
  } catch (error) {
    console.error('Error creating test ticket:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
