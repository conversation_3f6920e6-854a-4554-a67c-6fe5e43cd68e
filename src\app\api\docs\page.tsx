'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { ChevronRight, Code, FileJson, Share2, Handshake, Sparkles, BookOpen } from 'lucide-react';

export default function ApiDocsPage() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-5xl mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold mb-2">Event Platform API</h1>
          <p className="text-xl text-muted-foreground">
            Comprehensive documentation for integrating with our event platform
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
          <TabsList className="grid grid-cols-5 w-full">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="authentication">Authentication</TabsTrigger>
            <TabsTrigger value="events">Events</TabsTrigger>
            <TabsTrigger value="promotion">Promotion</TabsTrigger>
            <TabsTrigger value="tickets">Tickets</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5 text-primary" />
                  API Overview
                </CardTitle>
                <CardDescription>
                  Our RESTful API allows you to integrate with our event platform
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  The Event Platform API provides programmatic access to events, tickets, promotions, and more.
                  Use our API to build custom integrations, mobile apps, or extend the functionality of your website.
                </p>

                <h3 className="text-lg font-semibold mt-4">Base URL</h3>
                <div className="bg-muted p-3 rounded-md font-mono text-sm">
                  https://yourdomain.com/api
                </div>

                <h3 className="text-lg font-semibold mt-4">Response Format</h3>
                <p>
                  All responses are returned in JSON format. Successful responses will have a 2xx status code,
                  while errors will have a 4xx or 5xx status code.
                </p>

                <h3 className="text-lg font-semibold mt-4">Rate Limiting</h3>
                <p>
                  API requests are limited to 100 requests per minute per API key. If you exceed this limit,
                  you will receive a 429 Too Many Requests response.
                </p>

                <h3 className="text-lg font-semibold mt-4">Available APIs</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  <Card className="hover:bg-muted/50 transition-colors cursor-pointer">
                    <CardHeader className="p-4">
                      <CardTitle className="text-base flex items-center">
                        <Code className="h-4 w-4 mr-2 text-primary" />
                        Events API
                      </CardTitle>
                      <CardDescription className="text-xs">
                        Create, read, update, and delete events
                      </CardDescription>
                    </CardHeader>
                  </Card>
                  <Card className="hover:bg-muted/50 transition-colors cursor-pointer">
                    <CardHeader className="p-4">
                      <CardTitle className="text-base flex items-center">
                        <Share2 className="h-4 w-4 mr-2 text-primary" />
                        Promotion API
                      </CardTitle>
                      <CardDescription className="text-xs">
                        Manage event promotions and marketing
                      </CardDescription>
                    </CardHeader>
                  </Card>
                  <Card className="hover:bg-muted/50 transition-colors cursor-pointer">
                    <CardHeader className="p-4">
                      <CardTitle className="text-base flex items-center">
                        <FileJson className="h-4 w-4 mr-2 text-primary" />
                        Tickets API
                      </CardTitle>
                      <CardDescription className="text-xs">
                        Sell and manage event tickets
                      </CardDescription>
                    </CardHeader>
                  </Card>
                  <Card className="hover:bg-muted/50 transition-colors cursor-pointer">
                    <CardHeader className="p-4">
                      <CardTitle className="text-base flex items-center">
                        <Sparkles className="h-4 w-4 mr-2 text-primary" />
                        Analytics API
                      </CardTitle>
                      <CardDescription className="text-xs">
                        Access event performance data
                      </CardDescription>
                    </CardHeader>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="promotion" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Share2 className="h-5 w-5 text-primary" />
                  Promotion API
                </CardTitle>
                <CardDescription>
                  APIs for managing event promotions, referrals, and marketing campaigns
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">Featured Events API</h3>
                  <p className="text-muted-foreground">
                    Access and manage featured events on the platform
                  </p>

                  <div className="space-y-4 mt-4">
                    <div className="border rounded-md">
                      <div className="flex items-center justify-between p-4 border-b">
                        <div className="flex items-center">
                          <span className="bg-green-100 text-green-800 font-mono text-xs px-2 py-1 rounded mr-2">GET</span>
                          <span className="font-mono text-sm">/api/events/published?featured=true</span>
                        </div>
                        <Link href="/api/docs/featuring" passHref>
                          <Button variant="ghost" size="sm">
                            View Docs <ChevronRight className="h-4 w-4 ml-1" />
                          </Button>
                        </Link>
                      </div>
                      <div className="p-4 bg-muted/50">
                        <p className="text-sm">Get all featured events</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4 pt-6 border-t">
                  <h3 className="text-xl font-semibold">Referral API</h3>
                  <p className="text-muted-foreground">
                    Create and manage referral links for events to incentivize sharing
                  </p>

                  <div className="space-y-4 mt-4">
                    <div className="border rounded-md">
                      <div className="flex items-center justify-between p-4 border-b">
                        <div className="flex items-center">
                          <span className="bg-green-100 text-green-800 font-mono text-xs px-2 py-1 rounded mr-2">GET</span>
                          <span className="font-mono text-sm">/api/referrals</span>
                        </div>
                        <Button variant="ghost" size="sm">
                          Try it <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                      <div className="p-4 bg-muted/50">
                        <p className="text-sm">Get all referrals for the current user</p>
                      </div>
                    </div>

                    <div className="border rounded-md">
                      <div className="flex items-center justify-between p-4 border-b">
                        <div className="flex items-center">
                          <span className="bg-blue-100 text-blue-800 font-mono text-xs px-2 py-1 rounded mr-2">POST</span>
                          <span className="font-mono text-sm">/api/referrals</span>
                        </div>
                        <Button variant="ghost" size="sm">
                          Try it <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                      <div className="p-4 bg-muted/50">
                        <p className="text-sm">Create a new referral link for an event</p>
                      </div>
                    </div>

                    <div className="border rounded-md">
                      <div className="flex items-center justify-between p-4 border-b">
                        <div className="flex items-center">
                          <span className="bg-green-100 text-green-800 font-mono text-xs px-2 py-1 rounded mr-2">GET</span>
                          <span className="font-mono text-sm">/api/referrals/{'{code}'}</span>
                        </div>
                        <Button variant="ghost" size="sm">
                          Try it <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                      <div className="p-4 bg-muted/50">
                        <p className="text-sm">Get details about a specific referral link</p>
                      </div>
                    </div>

                    <div className="border rounded-md">
                      <div className="flex items-center justify-between p-4 border-b">
                        <div className="flex items-center">
                          <span className="bg-blue-100 text-blue-800 font-mono text-xs px-2 py-1 rounded mr-2">POST</span>
                          <span className="font-mono text-sm">/api/referrals/{'{code}'}/redeem</span>
                        </div>
                        <Button variant="ghost" size="sm">
                          Try it <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                      <div className="p-4 bg-muted/50">
                        <p className="text-sm">Redeem a referral link</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4 pt-6 border-t">
                  <h3 className="text-xl font-semibold">Cross-Promotion API</h3>
                  <p className="text-muted-foreground">
                    Create partnerships between events for mutual promotion
                  </p>

                  <div className="space-y-4 mt-4">
                    <div className="border rounded-md">
                      <div className="flex items-center justify-between p-4 border-b">
                        <div className="flex items-center">
                          <span className="bg-green-100 text-green-800 font-mono text-xs px-2 py-1 rounded mr-2">GET</span>
                          <span className="font-mono text-sm">/api/cross-promotions</span>
                        </div>
                        <Button variant="ghost" size="sm">
                          Try it <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                      <div className="p-4 bg-muted/50">
                        <p className="text-sm">Get all cross-promotions for the current user's events</p>
                      </div>
                    </div>

                    <div className="border rounded-md">
                      <div className="flex items-center justify-between p-4 border-b">
                        <div className="flex items-center">
                          <span className="bg-blue-100 text-blue-800 font-mono text-xs px-2 py-1 rounded mr-2">POST</span>
                          <span className="font-mono text-sm">/api/cross-promotions</span>
                        </div>
                        <Button variant="ghost" size="sm">
                          Try it <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                      <div className="p-4 bg-muted/50">
                        <p className="text-sm">Create a new cross-promotion request</p>
                      </div>
                    </div>

                    <div className="border rounded-md">
                      <div className="flex items-center justify-between p-4 border-b">
                        <div className="flex items-center">
                          <span className="bg-green-100 text-green-800 font-mono text-xs px-2 py-1 rounded mr-2">GET</span>
                          <span className="font-mono text-sm">/api/cross-promotions/{'{id}'}</span>
                        </div>
                        <Button variant="ghost" size="sm">
                          Try it <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                      <div className="p-4 bg-muted/50">
                        <p className="text-sm">Get details about a specific cross-promotion</p>
                      </div>
                    </div>

                    <div className="border rounded-md">
                      <div className="flex items-center justify-between p-4 border-b">
                        <div className="flex items-center">
                          <span className="bg-yellow-100 text-yellow-800 font-mono text-xs px-2 py-1 rounded mr-2">PATCH</span>
                          <span className="font-mono text-sm">/api/cross-promotions/{'{id}'}</span>
                        </div>
                        <Button variant="ghost" size="sm">
                          Try it <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                      <div className="p-4 bg-muted/50">
                        <p className="text-sm">Update the status of a cross-promotion</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4 pt-6 border-t">
                  <h3 className="text-xl font-semibold">Promotion Strategy API</h3>
                  <p className="text-muted-foreground">
                    Create and manage AI-powered promotion strategies for events
                  </p>

                  <div className="space-y-4 mt-4">
                    <div className="border rounded-md">
                      <div className="flex items-center justify-between p-4 border-b">
                        <div className="flex items-center">
                          <span className="bg-green-100 text-green-800 font-mono text-xs px-2 py-1 rounded mr-2">GET</span>
                          <span className="font-mono text-sm">/api/promotion-strategies</span>
                        </div>
                        <Button variant="ghost" size="sm">
                          Try it <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                      <div className="p-4 bg-muted/50">
                        <p className="text-sm">Get all promotion strategies for the current user</p>
                      </div>
                    </div>

                    <div className="border rounded-md">
                      <div className="flex items-center justify-between p-4 border-b">
                        <div className="flex items-center">
                          <span className="bg-blue-100 text-blue-800 font-mono text-xs px-2 py-1 rounded mr-2">POST</span>
                          <span className="font-mono text-sm">/api/promotion-strategies</span>
                        </div>
                        <Button variant="ghost" size="sm">
                          Try it <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                      <div className="p-4 bg-muted/50">
                        <p className="text-sm">Create a new promotion strategy</p>
                      </div>
                    </div>

                    <div className="border rounded-md">
                      <div className="flex items-center justify-between p-4 border-b">
                        <div className="flex items-center">
                          <span className="bg-green-100 text-green-800 font-mono text-xs px-2 py-1 rounded mr-2">GET</span>
                          <span className="font-mono text-sm">/api/promotion-strategies/{'{id}'}</span>
                        </div>
                        <Button variant="ghost" size="sm">
                          Try it <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                      <div className="p-4 bg-muted/50">
                        <p className="text-sm">Get details about a specific promotion strategy</p>
                      </div>
                    </div>

                    <div className="border rounded-md">
                      <div className="flex items-center justify-between p-4 border-b">
                        <div className="flex items-center">
                          <span className="bg-yellow-100 text-yellow-800 font-mono text-xs px-2 py-1 rounded mr-2">PATCH</span>
                          <span className="font-mono text-sm">/api/promotion-strategies/{'{id}'}</span>
                        </div>
                        <Button variant="ghost" size="sm">
                          Try it <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                      <div className="p-4 bg-muted/50">
                        <p className="text-sm">Update a promotion strategy</p>
                      </div>
                    </div>

                    <div className="border rounded-md">
                      <div className="flex items-center justify-between p-4 border-b">
                        <div className="flex items-center">
                          <span className="bg-yellow-100 text-yellow-800 font-mono text-xs px-2 py-1 rounded mr-2">PATCH</span>
                          <span className="font-mono text-sm">/api/promotion-strategies/{'{id}'}/recommendations/{'{recommendationId}'}</span>
                        </div>
                        <Button variant="ghost" size="sm">
                          Try it <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                      <div className="p-4 bg-muted/50">
                        <p className="text-sm">Update a recommendation status (implement or reject)</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="authentication">
            <Card>
              <CardHeader>
                <CardTitle>Authentication API</CardTitle>
                <CardDescription>
                  Authenticate with the API to access protected endpoints
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p>Authentication documentation coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="events">
            <Card>
              <CardHeader>
                <CardTitle>Events API</CardTitle>
                <CardDescription>
                  Create, read, update, and delete events
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p>Events API documentation coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tickets">
            <Card>
              <CardHeader>
                <CardTitle>Tickets API</CardTitle>
                <CardDescription>
                  Sell and manage event tickets
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p>Tickets API documentation coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="mt-12 text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to get started?</h2>
          <div className="flex justify-center gap-4">
            <Button size="lg">
              Get API Key
            </Button>
            <Button variant="outline" size="lg">
              View SDKs
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
