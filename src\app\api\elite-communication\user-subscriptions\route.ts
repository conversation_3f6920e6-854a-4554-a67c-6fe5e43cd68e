import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';
import { db } from '@/lib/prisma';

/**
 * GET /api/elite-communication/user-subscriptions
 * Get all Elite Communication subscriptions for the current user
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all subscriptions for the user
    const subscriptions = await db.eliteCommunication.findMany({
      where: {
        userId: session.user.id
      },
      include: {
        event: {
          select: {
            id: true,
            title: true,
            startDate: true,
            endDate: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Get current active subscription (highest tier)
    const activeSubscriptions = subscriptions.filter(sub => sub.isActive);
    const currentTier = activeSubscriptions.length > 0
      ? activeSubscriptions.reduce((highest, current) => {
          const tierOrder = { 'BASIC': 0, 'ELITE': 1, 'ELITE_PRO': 2 };
          return tierOrder[current.tier] > tierOrder[highest.tier] ? current : highest;
        }).tier
      : 'BASIC';

    return NextResponse.json({
      success: true,
      subscriptions: subscriptions,
      currentTier: currentTier,
      hasActiveSubscription: activeSubscriptions.length > 0
    });

  } catch (error) {
    console.error('Error fetching user subscriptions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/elite-communication/user-subscriptions
 * Cancel a specific subscription
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { subscriptionId } = await request.json();

    if (!subscriptionId) {
      return NextResponse.json({ error: 'Subscription ID is required' }, { status: 400 });
    }

    // Verify the subscription belongs to the user
    const subscription = await db.eliteCommunication.findFirst({
      where: {
        id: subscriptionId,
        userId: session.user.id
      }
    });

    if (!subscription) {
      return NextResponse.json({ error: 'Subscription not found' }, { status: 404 });
    }

    // Cancel the subscription
    await db.eliteCommunication.update({
      where: {
        id: subscriptionId
      },
      data: {
        isActive: false,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Subscription cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling subscription:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
