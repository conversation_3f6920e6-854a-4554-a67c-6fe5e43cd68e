import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/prisma";
import { currentUser } from "@/lib/auth";
import { sendEmail } from "@/lib/email";
import { addEmailTracking } from "@/lib/email-tracking";

// POST: Send a campaign to recipients
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    // Get the campaign
    const campaign = await db.marketingCampaign.findUnique({
      where: {
        id,
        userId: user.id!, // Ensure the campaign belongs to the current user
      },
      include: {
        event: true,
        recipients: {
          include: {
            user: true,
          },
        },
      },
    });

    if (!campaign) {
      return NextResponse.json({ error: "Campaign not found" }, { status: 404 });
    }

    // Check if campaign is already sent
    if (campaign.status === "SENT") {
      return NextResponse.json(
        { error: "Campaign has already been sent" },
        { status: 400 }
      );
    }

    // Get recipients based on audience type
    let recipients: { id: string; email: string; name?: string }[] = [];

    // If we already have recipients, use them
    if (campaign.recipients && campaign.recipients.length > 0) {
      recipients = campaign.recipients
        .filter((r) => r.user?.email) // Only include recipients with valid emails
        .map((r) => ({
          id: r.userId,
          email: r.user.email!,
          name: r.user.name || undefined,
        }));
    } else {
      // Otherwise, determine recipients based on audience type
      switch (campaign.audienceType) {
        case "all":
          // Get all users
          const allUsers = await db.user.findMany({
            where: {
              // Exclude users who have opted out of email marketing
              marketingPreferences: {
                emailOptIn: true,
              },
            },
            select: {
              id: true,
              email: true,
              name: true,
            },
          });
          recipients = allUsers
            .filter((u) => u.email) // Only include users with valid emails
            .map((u) => ({
              id: u.id,
              email: u.email!,
              name: u.name || undefined,
            }));
          break;

        case "segment":
          // Handle segmentation based on campaign.audienceSegment
          // This is a simplified example - you would implement more complex segmentation logic
          if (campaign.audienceSegment === "recent-customers") {
            const recentCustomers = await db.user.findMany({
              where: {
                lastPurchaseDate: {
                  gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
                },
                marketingPreferences: {
                  emailOptIn: true,
                },
              },
              select: {
                id: true,
                email: true,
                name: true,
              },
            });
            recipients = recentCustomers
              .filter((u) => u.email)
              .map((u) => ({
                id: u.id,
                email: u.email!,
                name: u.name || undefined,
              }));
          } else if (campaign.audienceSegment === "event-attendees" && campaign.eventId) {
            // Get users who have purchased tickets for this event
            const eventAttendees = await db.user.findMany({
              where: {
                orders: {
                  some: {
                    eventId: campaign.eventId,
                  },
                },
                marketingPreferences: {
                  emailOptIn: true,
                },
              },
              select: {
                id: true,
                email: true,
                name: true,
              },
            });
            recipients = eventAttendees
              .filter((u) => u.email)
              .map((u) => ({
                id: u.id,
                email: u.email!,
                name: u.name || undefined,
              }));
          }
          break;

        default:
          // No recipients
          break;
      }

      // Create recipient records for each recipient
      for (const recipient of recipients) {
        await db.marketingCampaignRecipient.create({
          data: {
            campaignId: campaign.id,
            userId: recipient.id,
            sentAt: new Date(),
          },
        });
      }
    }

    // Send emails to all recipients
    const results = [];
    for (const recipient of recipients) {
      try {
        // Replace template variables
        let personalizedContent = campaign.content;

        // Replace basic variables
        personalizedContent = personalizedContent
          .replace(/{{name}}/g, recipient.name || "there")
          .replace(/{{email}}/g, recipient.email);

        // Replace event variables if an event is associated
        if (campaign.event) {
          personalizedContent = personalizedContent
            .replace(/{{event.title}}/g, campaign.event.title || "")
            .replace(/{{event.startDate}}/g, campaign.event.startDate ? new Date(campaign.event.startDate).toLocaleDateString() : "")
            .replace(/{{event.endDate}}/g, campaign.event.endDate ? new Date(campaign.event.endDate).toLocaleDateString() : "");
        }

        // Create the email record first so we have an ID for tracking
        const emailRecord = await db.email.create({
          data: {
            from: "<EMAIL>", // Use your actual from address
            to: recipient.email,
            subject: campaign.subject || "No Subject",
            preview: campaign.previewText || "",
            category: "marketing",
            date: new Date(),
            read: false,
            userId: recipient.id,
          },
        });

        // Add tracking to the email content
        const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
        const trackedContent = addEmailTracking(personalizedContent, emailRecord.id, baseUrl);

        // Send the email with tracking
        const emailResult = await sendEmail({
          to: recipient.email,
          subject: campaign.subject || "No Subject",
          html: trackedContent,
          text: campaign.previewText || "",
        });

        // Update the email record with the external ID
        await db.email.update({
          where: { id: emailRecord.id },
          data: {
            externalId: typeof emailResult === 'object' && emailResult !== null && emailResult.id ? emailResult.id : null,
          },
        });

        // Update the campaign recipient record with the email ID
        await db.marketingCampaignRecipient.updateMany({
          where: {
            campaignId: campaign.id,
            userId: recipient.id,
          },
          data: {
            emailId: emailRecord.id,
          },
        });

        results.push({
          recipient: recipient.email,
          success: true,
          emailId: emailRecord.id,
        });
      } catch (error) {
        console.error(`Error sending email to ${recipient.email}:`, error);
        results.push({
          recipient: recipient.email,
          success: false,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    // Update campaign status
    await db.marketingCampaign.update({
      where: { id },
      data: {
        status: "SENT",
        sentAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      campaignId: campaign.id,
      recipientCount: recipients.length,
      results,
    });
  } catch (error) {
    console.error("Error sending campaign:", error);
    return NextResponse.json(
      { error: "Failed to send campaign" },
      { status: 500 }
    );
  }
}
