import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/prisma";
import { currentUser } from "@/lib/auth";
import { sendEmail } from "@/lib/email";

// GET: Retrieve email campaigns
export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get URL parameters for filtering
    const url = new URL(request.url);
    const status = url.searchParams.get('status');
    const type = url.searchParams.get('type');
    const eventId = url.searchParams.get('eventId');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const page = parseInt(url.searchParams.get('page') || '1');
    const skip = (page - 1) * limit;

    // Build query
    const where = {
      userId: user.id!,
      ...(status ? { status } : {}),
      ...(type ? { type } : {}),
      ...(eventId ? { eventId } : {}),
    };

    // Fetch campaigns with pagination
    const [campaigns, totalCount] = await Promise.all([
      db.marketingCampaign.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
        include: {
          event: {
            select: {
              id: true,
              title: true,
              startDate: true,
              endDate: true,
              imagePath: true,
            },
          },
          recipients: {
            select: {
              id: true,
              sentAt: true,
              openedAt: true,
              clickedAt: true,
            },
          },
        },
      }),
      db.marketingCampaign.count({ where }),
    ]);

    // Calculate metrics for each campaign
    const campaignsWithMetrics = campaigns.map(campaign => {
      const totalRecipients = campaign.recipients.length;
      const openCount = campaign.recipients.filter(r => r.openedAt).length;
      const clickCount = campaign.recipients.filter(r => r.clickedAt).length;
      
      return {
        ...campaign,
        metrics: {
          totalRecipients,
          openCount,
          clickCount,
          openRate: totalRecipients > 0 ? (openCount / totalRecipients) * 100 : 0,
          clickRate: totalRecipients > 0 ? (clickCount / totalRecipients) * 100 : 0,
        }
      };
    });

    return NextResponse.json({
      campaigns: campaignsWithMetrics,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching campaigns:', error);
    return NextResponse.json(
      { error: 'Failed to fetch campaigns' },
      { status: 500 }
    );
  }
}

// POST: Create a new email campaign
export async function POST(request: NextRequest) {
  try {
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const {
      name,
      description,
      type,
      content,
      eventId,
      subject,
      previewText,
      audienceType,
      audienceSegment,
      scheduledDate,
      recipientEmails,
    } = await request.json();

    // Validate required fields
    if (!name || !type || !content || !subject) {
      return NextResponse.json(
        { error: 'Name, type, content, and subject are required' },
        { status: 400 }
      );
    }

    // Create campaign
    const campaign = await db.marketingCampaign.create({
      data: {
        name,
        description: description || '',
        type,
        content,
        eventId: eventId || null,
        userId: user.id!,
        status: scheduledDate ? 'SCHEDULED' : 'DRAFT',
        scheduledDate: scheduledDate ? new Date(scheduledDate) : null,
        subject: subject || '',
        previewText: previewText || '',
        audienceType: audienceType || 'all',
        audienceSegment: audienceSegment || ''
      }
    });

    // If recipient emails were provided, create recipient records
    if (recipientEmails && Array.isArray(recipientEmails) && recipientEmails.length > 0) {
      // Find or create users for each email
      for (const email of recipientEmails) {
        // Find user by email or use a placeholder ID
        let recipientUser = await db.user.findUnique({
          where: { email },
          select: { id: true }
        });

        if (!recipientUser) {
          // Create a placeholder user if needed
          recipientUser = await db.user.create({
            data: {
              email,
              name: email.split('@')[0], // Use part of email as name
              role: 'USER',
            },
            select: { id: true }
          });
        }

        // Create recipient record
        await db.marketingCampaignRecipient.create({
          data: {
            campaignId: campaign.id,
            userId: recipientUser.id,
            sentAt: new Date(), // Mark as sent immediately for now
          }
        });
      }
    }

    return NextResponse.json(campaign, { status: 201 });
  } catch (error) {
    console.error('Error creating campaign:', error);
    return NextResponse.json(
      { error: 'Failed to create campaign' },
      { status: 500 }
    );
  }
}
