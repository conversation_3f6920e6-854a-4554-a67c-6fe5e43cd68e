import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/prisma";

// GET: Track email opens
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Find the email
    const email = await db.email.findUnique({
      where: { id },
      select: { id: true, userId: true, category: true },
    });

    if (!email) {
      // Return a transparent 1x1 pixel GIF even if email not found
      return new NextResponse(
        Buffer.from(
          "R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",
          "base64"
        ),
        {
          headers: {
            "Content-Type": "image/gif",
            "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate",
            Pragma: "no-cache",
            Expires: "0",
          },
        }
      );
    }

    // If this is a marketing email, update the campaign recipient record
    if (email.category === "marketing") {
      // Find the campaign recipient
      const campaignRecipient = await db.marketingCampaignRecipient.findFirst({
        where: {
          userId: email.userId,
          campaign: {
            recipients: {
              some: {
                userId: email.userId,
              },
            },
          },
        },
      });

      if (campaignRecipient) {
        // Update the openedAt timestamp if not already set
        if (!campaignRecipient.openedAt) {
          await db.marketingCampaignRecipient.update({
            where: { id: campaignRecipient.id },
            data: { openedAt: new Date() },
          });
        }
      }
    }

    // Return a transparent 1x1 pixel GIF
    return new NextResponse(
      Buffer.from(
        "R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",
        "base64"
      ),
      {
        headers: {
          "Content-Type": "image/gif",
          "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      }
    );
  } catch (error) {
    console.error("Error tracking email open:", error);
    
    // Still return a transparent pixel even if there's an error
    return new NextResponse(
      Buffer.from(
        "R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",
        "base64"
      ),
      {
        headers: {
          "Content-Type": "image/gif",
          "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      }
    );
  }
}
