import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/prisma";

// GET: Track email clicks and redirect to the target URL
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const url = new URL(request.url);
    const targetUrl = url.searchParams.get("url");

    if (!targetUrl) {
      return NextResponse.json(
        { error: "Missing target URL" },
        { status: 400 }
      );
    }

    // Find the email
    const email = await db.email.findUnique({
      where: { id },
      select: { id: true, userId: true, category: true },
    });

    if (email) {
      // If this is a marketing email, update the campaign recipient record
      if (email.category === "marketing") {
        // Find the campaign recipient
        const campaignRecipient = await db.marketingCampaignRecipient.findFirst({
          where: {
            userId: email.userId,
            campaign: {
              recipients: {
                some: {
                  userId: email.userId,
                },
              },
            },
          },
        });

        if (campaignRecipient) {
          // Update the clickedAt timestamp if not already set
          if (!campaignRecipient.clickedAt) {
            await db.marketingCampaignRecipient.update({
              where: { id: campaignRecipient.id },
              data: { clickedAt: new Date() },
            });
          }
        }
      }
    }

    // Redirect to the target URL
    return NextResponse.redirect(targetUrl);
  } catch (error) {
    console.error("Error tracking email click:", error);
    
    // If there's an error, redirect to the home page
    return NextResponse.redirect(new URL("/", request.url));
  }
}
