import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/prisma";

// GET: Track email opens
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Find the email
    const email = await db.email.findUnique({
      where: { id },
      select: { id: true, userId: true, category: true },
    });

    if (!email) {
      // Return a 1x1 transparent GIF even if the email is not found
      return new NextResponse(
        Buffer.from(
          "R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",
          "base64"
        ),
        {
          headers: {
            "Content-Type": "image/gif",
            "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate",
            Pragma: "no-cache",
            Expires: "0",
          },
        }
      );
    }

    // Update the email as read
    await db.email.update({
      where: { id },
      data: { read: true },
    });

    // If this is a marketing email, update the campaign recipient
    if (email.category === "marketing") {
      // Find the campaign recipient
      const recipient = await db.marketingCampaignRecipient.findFirst({
        where: {
          userId: email.userId,
          campaign: {
            recipients: {
              some: {
                userId: email.userId,
              },
            },
          },
        },
      });

      if (recipient) {
        // Update the recipient's open status if not already opened
        if (!recipient.openedAt) {
          await db.marketingCampaignRecipient.update({
            where: { id: recipient.id },
            data: { openedAt: new Date() },
          });
        }
      }
    }

    // Return a 1x1 transparent GIF
    return new NextResponse(
      Buffer.from(
        "R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",
        "base64"
      ),
      {
        headers: {
          "Content-Type": "image/gif",
          "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      }
    );
  } catch (error) {
    console.error("Error tracking email open:", error);
    
    // Return a 1x1 transparent GIF even if there's an error
    return new NextResponse(
      Buffer.from(
        "R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",
        "base64"
      ),
      {
        headers: {
          "Content-Type": "image/gif",
          "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      }
    );
  }
}
