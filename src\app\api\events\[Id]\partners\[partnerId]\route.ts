import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/events/:id/partners/:partnerId
 * Get a specific event partner
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; partnerId: string }> }
) {
  try {
    const { id, partnerId } = await params;
    const eventId = id;

    // Get the event partner
    const eventPartner = await db.eventPartner.findUnique({
      where: {
        eventId_partnerId: {
          eventId,
          partnerId,
        },
      },
      include: {
        partner: {
          select: {
            id: true,
            businessName: true,
            partnerType: true,
            description: true,
            address: true,
            city: true,
            province: true,
            logo: true,
            bannerImage: true,
            rating: true,
            totalReviews: true,
            acceptsNfcPayments: true,
            amenities: true,
            priceRange: true,
            website: true,
            contactPhone: true,
            contactEmail: true,
          },
        },
        event: {
          select: {
            id: true,
            title: true,
            startDate: true,
            endDate: true,
            venue: true,
            location: true,
            imagePath: true,
          },
        },
      },
    });

    if (!eventPartner) {
      return NextResponse.json({ error: 'Event partner not found' }, { status: 404 });
    }

    return NextResponse.json(eventPartner);
  } catch (error) {
    console.error('Error fetching event partner:', error);
    return NextResponse.json(
      { error: 'Failed to fetch event partner' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/events/:id/partners/:partnerId
 * Update an event partner
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; partnerId: string }> }
) {
  try {
    const user = await currentUser();
    const { id, partnerId } = await params;
    const eventId = id;

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if the event exists and belongs to the user
    const event = await db.event.findUnique({
      where: { id: eventId },
    });

    if (!event) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    // Check if the user is the event owner, team member, or an admin
    const isOwner = event.userId === user.id;
    const isAdmin = user.role === 'ADMIN' || user.role === 'SUPERADMIN';
    
    let isTeamMember = false;
    if (event.teamId) {
      const teamMembership = await db.teamMember.findFirst({
        where: {
          teamId: event.teamId,
          userId: user.id!,
        },
      });
      isTeamMember = !!teamMembership;
    }

    if (!isOwner && !isAdmin && !isTeamMember) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Check if the event partner exists
    const existingPartnership = await db.eventPartner.findUnique({
      where: {
        eventId_partnerId: {
          eventId,
          partnerId,
        },
      },
    });

    if (!existingPartnership) {
      return NextResponse.json({ error: 'Event partner not found' }, { status: 404 });
    }

    const body = await request.json();
    const { partnerType, specialOffer, isActive } = body;

    // Update the event partner
    const updatedPartnership = await db.eventPartner.update({
      where: { id: existingPartnership.id },
      data: {
        partnerType,
        specialOffer,
        isActive,
      },
    });

    return NextResponse.json(updatedPartnership);
  } catch (error) {
    console.error('Error updating event partner:', error);
    return NextResponse.json(
      { error: 'Failed to update event partner' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/events/:id/partners/:partnerId
 * Remove a partner from an event
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; partnerId: string }> }
) {
  try {
    const user = await currentUser();
    const { id, partnerId } = await params;
    const eventId = id;

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if the event exists and belongs to the user
    const event = await db.event.findUnique({
      where: { id: eventId },
    });

    if (!event) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    // Check if the user is the event owner, team member, or an admin
    const isOwner = event.userId === user.id;
    const isAdmin = user.role === 'ADMIN' || user.role === 'SUPERADMIN';
    
    let isTeamMember = false;
    if (event.teamId) {
      const teamMembership = await db.teamMember.findFirst({
        where: {
          teamId: event.teamId,
          userId: user.id!,
        },
      });
      isTeamMember = !!teamMembership;
    }

    if (!isOwner && !isAdmin && !isTeamMember) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Check if the event partner exists
    const existingPartnership = await db.eventPartner.findUnique({
      where: {
        eventId_partnerId: {
          eventId,
          partnerId,
        },
      },
    });

    if (!existingPartnership) {
      return NextResponse.json({ error: 'Event partner not found' }, { status: 404 });
    }

    // Delete the event partner
    await db.eventPartner.delete({
      where: { id: existingPartnership.id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error removing event partner:', error);
    return NextResponse.json(
      { error: 'Failed to remove event partner' },
      { status: 500 }
    );
  }
}
