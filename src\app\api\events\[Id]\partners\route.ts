import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/events/:id/partners
 * Get partners for a specific event
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Handle both Promise and direct params
    const resolvedParams = await Promise.resolve(params);
    const eventId = resolvedParams.id;

    // Check if the event exists and is published
    const event = await db.event.findUnique({
      where: {
        id: eventId,
        status: 'Published',
      },
    });

    if (!event) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const partnerType = searchParams.get('type');

    // Build the where clause
    const where: any = {
      eventId,
      isActive: true,
    };

    if (partnerType) {
      where.partnerType = partnerType;
    }

    // Get event partners
    const eventPartners = await db.eventPartner.findMany({
      where,
      include: {
        partner: {
          select: {
            id: true,
            businessName: true,
            partnerType: true,
            description: true,
            address: true,
            city: true,
            logo: true,
            rating: true,
            totalReviews: true,
            acceptsNfcPayments: true,
            amenities: true,
            priceRange: true,
          },
        },
      },
      orderBy: [
        { partnerType: 'asc' },
        { createdAt: 'asc' },
      ],
    });

    return NextResponse.json(eventPartners);
  } catch (error) {
    console.error('Error fetching event partners:', error);
    return NextResponse.json(
      { error: 'Failed to fetch event partners' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/events/:id/partners
 * Add a partner to an event
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await currentUser();
    // Handle both Promise and direct params
    const resolvedParams = await Promise.resolve(params);
    const eventId = resolvedParams.id;

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if the event exists and belongs to the user
    const event = await db.event.findUnique({
      where: { id: eventId },
    });

    if (!event) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    // Check if the user is the event owner, team member, or an admin
    const isOwner = event.userId === user.id;
    const isAdmin = user.role === 'ADMIN' || user.role === 'SUPERADMIN';
    
    let isTeamMember = false;
    if (event.teamId) {
      const teamMembership = await db.teamMember.findFirst({
        where: {
          teamId: event.teamId,
          userId: user.id!,
        },
      });
      isTeamMember = !!teamMembership;
    }

    if (!isOwner && !isAdmin && !isTeamMember) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const body = await request.json();
    const { partnerId, partnerType, specialOffer } = body;

    // Validate required fields
    if (!partnerId || !partnerType) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if the partner exists
    const partner = await db.partner.findUnique({
      where: { id: partnerId },
    });

    if (!partner) {
      return NextResponse.json({ error: 'Partner not found' }, { status: 404 });
    }

    // Check if the partnership already exists
    const existingPartnership = await db.eventPartner.findUnique({
      where: {
        eventId_partnerId: {
          eventId,
          partnerId,
        },
      },
    });

    if (existingPartnership) {
      // Update existing partnership
      const updatedPartnership = await db.eventPartner.update({
        where: { id: existingPartnership.id },
        data: {
          partnerType,
          specialOffer,
          isActive: true,
        },
      });
      return NextResponse.json(updatedPartnership);
    }

    // Create new partnership
    const eventPartner = await db.eventPartner.create({
      data: {
        eventId,
        partnerId,
        partnerType,
        specialOffer,
      },
    });

    return NextResponse.json(eventPartner);
  } catch (error) {
    console.error('Error adding event partner:', error);
    return NextResponse.json(
      { error: 'Failed to add event partner' },
      { status: 500 }
    );
  }
}
