import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
    const resolvedParams = await params;

  try {
    console.log('Publish API called with params:', params);

    const user = await currentUser();
    console.log('Current user:', user ? `ID: ${user.id}, Role: ${user.role}` : 'No user');

    if (!user?.id) {
      console.log('Unauthorized: No user ID');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const eventId = resolvedParams.id;
    console.log('Event ID:', eventId);

    if (!eventId) {
      console.log('Event ID is required');
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 });
    }

    // Get the event
    const event = await db.event.findUnique({
      where: { id: eventId },
      select: {
        id: true,
        title: true,
        status: true,
        userId: true,
      },
    });

    console.log('Event found:', event);

    if (!event) {
      console.log('Event not found');
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    // Check if the user is the owner of the event or an admin
    const isOwner = event.userId === user.id;
    const isAdmin = user.role === 'ADMIN' || user.role === 'SUPERADMIN';

    if (!isOwner && !isAdmin) {
      console.log('Forbidden: User is not the owner or admin');
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Check if the event is in the correct status to be published
    if (event.status !== 'Approved') {
      console.log('Event is not in Approved status');
      return NextResponse.json(
        { error: 'Event must be in Approved status to be published' },
        { status: 400 }
      );
    }

    // Update the event status to Published
    const updatedEvent = await db.event.update({
      where: { id: eventId },
      data: { status: 'Published' },
      select: {
        id: true,
        title: true,
        status: true,
      },
    });

    console.log('Event updated:', updatedEvent);

    // Create a notification for the event owner
    if (!isOwner) {
      await db.notification.create({
        data: {
          userId: event.userId,
          message: `Your event "${event.title}" has been published.`,
          type: 'EVENT_PUBLISHED',
          isRead: false,
        },
      });
      console.log('Notification created for event owner');
    }

    return NextResponse.json({
      success: true,
      message: 'Event published successfully',
      event: updatedEvent,
    });
  } catch (error) {
    console.error('Error publishing event:', error);
    // Return more detailed error information
    const errorMessage = error instanceof Error
      ? `${error.name}: ${error.message}`
      : 'Unknown error';

    return NextResponse.json(
      {
        error: 'Failed to publish event',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
