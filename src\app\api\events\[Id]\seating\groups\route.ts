import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { z } from 'zod';

// Schema for creating seat groups
const createSeatGroupSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  type: z.string().min(1, 'Type is required'),
  capacity: z.number().int().positive('Capacity must be a positive integer'),
  section: z.string().min(1, 'Section is required'),
  x: z.number().optional(),
  y: z.number().optional(),
  notes: z.string().optional(),
  seats: z.array(z.object({
    row: z.string().optional(),
    number: z.number().int().positive(),
    label: z.string().optional(),
    accessible: z.boolean().optional(),
    restricted: z.boolean().optional(),
    notes: z.string().optional(),
  })).optional(),
});

// GET handler to fetch seat groups for an event
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
    const resolvedParams = await params;

  try {
    const eventId = resolvedParams.id;
    const { searchParams } = new URL(request.url);
    const section = searchParams.get('section');

    // Verify the event exists
    const event = await db.event.findUnique({
      where: { id: eventId },
    });

    if (!event) {
      return NextResponse.json({
        error: 'Event not found or you don\'t have permission to access it.'
      }, { status: 404 });
    }

    // Query parameters
    const query: any = { eventId };
    if (section) {
      query.section = section;
    }

    // Fetch seat groups
    const seatGroups = await db.seatGroup.findMany({
      where: query,
      include: {
        seats: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    return NextResponse.json(seatGroups);
  } catch (error) {
    console.error('Error fetching seat groups:', error);
    return NextResponse.json(
      { error: 'Failed to fetch seat groups' },
      { status: 500 }
    );
  }
}

// POST handler to create seat groups
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: eventId } = await params;
    const user = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify the event exists and user has permission
    const event = await db.event.findUnique({
      where: { id: eventId },
    });

    if (!event) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    if (event.userId !== user.id && user.role !== 'ADMIN' && user.role !== 'SUPERADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = createSeatGroupSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { seats, ...groupData } = validationResult.data;

    // Create the seat group
    const seatGroup = await db.seatGroup.create({
      data: {
        ...groupData,
        eventId,
      },
    });

    // Create seats if provided
    if (seats && seats.length > 0) {
      const seatPromises = seats.map((seat, index) => {
        return db.seat.create({
          data: {
            row: seat.row || seatGroup.name,
            number: seat.number || index + 1,
            section: groupData.section,
            category: 'Table', // Default category for grouped seats
            label: seat.label || `${seatGroup.name}-${index + 1}`,
            accessible: seat.accessible || false,
            restricted: seat.restricted || false,
            notes: seat.notes,
            eventId,
            groupId: seatGroup.id,
          },
        });
      });

      await Promise.all(seatPromises);
    }

    // Fetch the created group with seats
    const createdGroup = await db.seatGroup.findUnique({
      where: { id: seatGroup.id },
      include: {
        seats: true,
      },
    });

    return NextResponse.json(createdGroup);
  } catch (error) {
    console.error('Error creating seat group:', error);
    return NextResponse.json(
      { error: 'Failed to create seat group' },
      { status: 500 }
    );
  }
}

// DELETE handler to delete a seat group
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: eventId } = await params;
    const user = await currentUser();
    const { searchParams } = new URL(request.url);
    const groupId = searchParams.get('groupId');

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!groupId) {
      return NextResponse.json({ error: 'Group ID is required' }, { status: 400 });
    }

    // Verify the event exists and user has permission
    const event = await db.event.findUnique({
      where: { id: eventId },
    });

    if (!event) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    if (event.userId !== user.id && user.role !== 'ADMIN' && user.role !== 'SUPERADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Delete the seat group (seats will be deleted due to cascade)
    await db.seatGroup.delete({
      where: {
        id: groupId,
        eventId,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting seat group:', error);
    return NextResponse.json(
      { error: 'Failed to delete seat group' },
      { status: 500 }
    );
  }
}
