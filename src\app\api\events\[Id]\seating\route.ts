import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { SeatCategory } from '@prisma/client';
import {
  validateSeatCount,
  validateTicketPrices,
  validateVenueTypeForLayout
} from '@/lib/venue-seating-validator';

/**
 * GET /api/events/[id]/seating
 * Get seating sections for an event
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const eventId = resolvedParams.id;

    // Fetch all seats for the event, grouped by section
    const seats = await db.seat.findMany({
      where: { eventId },
      orderBy: [
        { section: 'asc' },
        { row: 'asc' },
        { number: 'asc' },
      ],
      // Select only the fields that definitely exist
      select: {
        id: true,
        row: true,
        number: true,
        section: true,
        category: true,
        isReserved: true,
        isOccupied: true,
        eventId: true,
        accessible: true,
        restricted: true,
        notes: true,
        groupId: true,
        venueType: true,
        // Optional fields
        label: true,
        x: true,
        y: true
      }
    });

    if (seats.length === 0) {
      return NextResponse.json([]);
    }

    // Group seats by section and calculate section properties
    const sectionMap = new Map();

    // Get all seat groups for this event
    const seatGroups = await db.seatGroup.findMany({
      where: { eventId },
      include: { seats: true },
    });

    // Create a map of group IDs to their layout types
    const groupLayoutMap = new Map();
    seatGroups.forEach(group => {
      groupLayoutMap.set(group.id, group.type);
    });

    seats.forEach(seat => {
      if (!sectionMap.has(seat.section)) {
        // Determine layout type based on seat properties
        let layoutType = 'rows';
        if (seat.groupId) {
          const groupType = groupLayoutMap.get(seat.groupId);
          if (groupType === 'Table') layoutType = 'tables';
          else if (groupType === 'Booth') layoutType = 'booths';
          else if (groupType === 'Lounge') layoutType = 'lounge';
          else if (groupType === 'Theater') layoutType = 'theater';
        }

        sectionMap.set(seat.section, {
          id: `section-${seat.section.replace(/\s+/g, '-').toLowerCase()}`,
          name: seat.section,
          category: seat.category,
          layoutType,
          venueType: seat.venueType || 'stadium', // Default to stadium if not specified
          seats: [],
          rows: new Set(),
          seatsPerRow: 0,
          price: 0, // Will be set based on category
        });
      }

      const section = sectionMap.get(seat.section);
      section.seats.push(seat);
      section.rows.add(seat.row);
    });

    // Get ticket prices directly from the database
    const tickets = await db.ticket.findMany({
      where: {
        eventId,
        isAvailable: true
      },
      select: {
        type: true,
        price: true
      },
      distinct: ['type']
    });

    // Create a map of ticket types to prices
    const ticketPrices = tickets.reduce((prices, ticket) => {
      prices[ticket.type] = ticket.price;
      return prices;
    }, {} as Record<string, number>);

    // If no tickets found, try to get prices from ticketSales as fallback
    if (Object.keys(ticketPrices).length === 0) {
      const ticketSales = await db.ticketSales.findMany({
        where: { eventId },
      });

      ticketSales.forEach(ts => {
        ticketPrices[ts.type] = ts.price;
      });
    }

    // Calculate price and other properties for each section
    const sections = Array.from(sectionMap.values()).map(section => {
      // Count seats in the first row to determine seatsPerRow
      const firstRow = section.seats.filter((seat: any) => seat.row === Array.from(section.rows)[0]);

      // Set price based on category - directly use ticket prices from database
      let price = 0;
      let color = '#3b82f6'; // Default blue

      // Direct mapping from section category to ticket type
      const categoryToTicketType: Record<string, string> = {
        'Regular': 'REGULAR',
        'VIP': 'VIP',
        'VVIP': 'VVIP'
      };

      // Get the ticket type for this section category
      const ticketType = categoryToTicketType[section.category as string];

      // Use the ticket price from the database if available
      if (ticketType && ticketPrices[ticketType]) {
        price = ticketPrices[ticketType];
      } else {
        // No fallback prices - if no ticket price is found, use 0
        price = 0;
      }

      // Set colors based on category
      switch (section.category) {
        case 'Regular':
          color = '#3b82f6'; // Blue
          break;
        case 'VIP':
          color = '#10b981'; // Green
          break;
        case 'VVIP':
          color = '#8b5cf6'; // Purple
          break;
        case 'Premium':
          color = '#f59e0b'; // Amber
          break;
        case 'Box':
          color = '#ec4899'; // Pink
          break;
        case 'Booth':
          color = '#8b5cf6'; // Purple
          break;
        case 'Table':
          color = '#06b6d4'; // Cyan
          break;
        case 'Accessible':
          color = '#3b82f6'; // Blue
          break;
        case 'RestrictedView':
          color = '#9ca3af'; // Gray
          break;
        case 'Standing':
          color = '#6b7280'; // Gray
          break;
      }

      return {
        id: section.id,
        name: section.name,
        rows: section.rows.size,
        seatsPerRow: firstRow.length,
        category: section.category,
        layoutType: section.layoutType,
        price,
        color,
      };
    });

    return NextResponse.json(sections);
  } catch (error) {
    console.error('Error fetching seating sections:', error);
    return NextResponse.json(
      { error: 'Failed to fetch seating sections' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/events/[id]/seating
 * Create or update seating sections for an event
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await currentUser();

    // Only allow authenticated users
    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const eventId = resolvedParams.id;
    const { sections } = await request.json();

    // Get ticket prices for validation
    const tickets = await db.ticket.findMany({
      where: {
        eventId,
        isAvailable: true
      },
      select: {
        type: true,
        price: true
      },
      distinct: ['type']
    });

    // Create a map of ticket types to prices
    const ticketPrices = tickets.reduce((prices, ticket) => {
      prices[ticket.type] = ticket.price;
      return prices;
    }, {} as Record<string, number>);

    // If no tickets found, try to get prices from ticketSales as fallback
    if (Object.keys(ticketPrices).length === 0) {
      const ticketSales = await db.ticketSales.findMany({
        where: { eventId },
      });

      ticketSales.forEach(ts => {
        ticketPrices[ts.type] = ts.price;
      });
    }

    // Check if the event exists and user has permission
    const event = await db.event.findUnique({
      where: { id: eventId },
      select: {
        id: true,
        userId: true,
      },
    });

    if (!event) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    // Check if user is authorized to modify this event
    const isOwner = event.userId === user.id;
    const isAdmin = user.role === 'ADMIN' || user.role === 'SUPERADMIN';

    if (!isOwner && !isAdmin) {
      return NextResponse.json(
        { error: 'You do not have permission to modify this event' },
        { status: 403 }
      );
    }

    // Delete existing seats for this event
    await db.seat.deleteMany({
      where: { eventId },
    });

    // Create seats for each section
    const createdSeats = [];

    for (const section of sections) {
      const {
        name,
        category,
        layoutType = 'rows',
        venueType = 'stadium',
        rows,
        seatsPerRow,
        tables,
        seatsPerTable,
        booths,
        seatsPerBooth,
        loungeAreas,
        seatsPerLounge,
        theaterRows,
        seatsPerTheaterRow,
        accessibleSeatsCount = 0
      } = section;

      // Validate category
      if (!Object.values(SeatCategory).includes(category as SeatCategory)) {
        return NextResponse.json(
          { error: `Invalid seat category: ${category}` },
          { status: 400 }
        );
      }

      // Validate venue type and layout compatibility
      const venueLayoutValidation = validateVenueTypeForLayout(venueType as any, layoutType);
      if (!venueLayoutValidation.valid) {
        return NextResponse.json(
          { error: venueLayoutValidation.message },
          { status: 400 }
        );
      }

      // Validate seat count consistency
      const seatCountValidation = validateSeatCount(section);
      if (!seatCountValidation.valid) {
        return NextResponse.json(
          {
            error: `Seat count mismatch in section "${name}"`,
            expected: seatCountValidation.expected,
            actual: seatCountValidation.actual
          },
          { status: 400 }
        );
      }

      // Validate ticket prices if we have ticket data
      if (Object.keys(ticketPrices).length > 0) {
        // Direct mapping from section category to ticket type
        const categoryToTicketType: Record<string, string> = {
          'Regular': 'REGULAR',
          'VIP': 'VIP',
          'VVIP': 'VVIP'
        };

        // Get the ticket type for this section category
        const ticketType = categoryToTicketType[category];

        // Only validate if we have a matching ticket type
        if (ticketType && ticketPrices[ticketType]) {
          const priceValidation = validateTicketPrices({
            ...section,
            category: ticketType
          }, ticketPrices);

          if (!priceValidation.valid) {
            console.warn(`Price mismatch in section "${name}": ${priceValidation.mismatches.join(', ')}`);
            // We're just warning here, not blocking, to allow flexibility
          }
        }
      }

      // Handle different layout types
      switch (layoutType) {
        case 'rows':
          // Create traditional row-based seating
          for (let rowIndex = 0; rowIndex < rows; rowIndex++) {
            const rowLabel = String.fromCharCode(65 + rowIndex); // A, B, C, ...

            for (let seatNumber = 1; seatNumber <= seatsPerRow; seatNumber++) {
              // Mark some seats as accessible if needed
              const isAccessible = accessibleSeatsCount > 0 &&
                rowIndex === rows - 1 && // Last row
                seatNumber <= accessibleSeatsCount; // First N seats

              const seat = await db.seat.create({
                data: {
                  eventId,
                  section: name,
                  row: rowLabel,
                  number: seatNumber,
                  category: isAccessible ? 'Accessible' as SeatCategory : category as SeatCategory,
                  isReserved: false,
                  isOccupied: false,
                  accessible: isAccessible,
                  venueType,
                },
              });

              createdSeats.push(seat);
            }
          }
          break;

        case 'tables':
          // Create table-based seating
          for (let tableIndex = 0; tableIndex < tables; tableIndex++) {
            const tableName = `T${tableIndex + 1}`;

            // Create a seat group for the table
            const seatGroup = await db.seatGroup.create({
              data: {
                eventId,
                name: tableName,
                type: 'Table',
                capacity: seatsPerTable,
                section: name,
                venueType,
              },
            });

            // Create seats for this table
            for (let seatNumber = 1; seatNumber <= seatsPerTable; seatNumber++) {
              // Calculate position around the table (for visualization)
              const angle = (seatNumber - 1) * (360 / seatsPerTable) * (Math.PI / 180);
              const radius = 45; // percentage of container
              const x = 50 + radius * Math.cos(angle);
              const y = 50 + radius * Math.sin(angle);

              const seat = await db.seat.create({
                data: {
                  eventId,
                  section: name,
                  row: tableName,
                  number: seatNumber,
                  category: category as SeatCategory,
                  isReserved: false,
                  isOccupied: false,
                  label: `${tableName}-${seatNumber}`,
                  groupId: seatGroup.id,
                  x,
                  y,
                  venueType,
                },
              });

              createdSeats.push(seat);
            }
          }
          break;

        case 'booths':
          // Create booth-based seating
          for (let boothIndex = 0; boothIndex < booths; boothIndex++) {
            const boothName = `B${boothIndex + 1}`;

            // Create a seat group for the booth
            const seatGroup = await db.seatGroup.create({
              data: {
                eventId,
                name: boothName,
                type: 'Booth',
                capacity: seatsPerBooth,
                section: name,
                venueType,
              },
            });

            // Create seats for this booth
            for (let seatNumber = 1; seatNumber <= seatsPerBooth; seatNumber++) {
              const seat = await db.seat.create({
                data: {
                  eventId,
                  section: name,
                  row: boothName,
                  number: seatNumber,
                  category: category as SeatCategory,
                  isReserved: false,
                  isOccupied: false,
                  label: `${boothName}-${seatNumber}`,
                  groupId: seatGroup.id,
                  venueType,
                },
              });

              createdSeats.push(seat);
            }
          }
          break;

        case 'lounge':
          // Create lounge-based seating
          for (let loungeIndex = 0; loungeIndex < loungeAreas; loungeIndex++) {
            const loungeName = `L${loungeIndex + 1}`;

            // Create a seat group for the lounge
            const seatGroup = await db.seatGroup.create({
              data: {
                eventId,
                name: loungeName,
                type: 'Lounge',
                capacity: seatsPerLounge,
                section: name,
                venueType,
              },
            });

            // Create seats for this lounge
            for (let seatNumber = 1; seatNumber <= seatsPerLounge; seatNumber++) {
              const seat = await db.seat.create({
                data: {
                  eventId,
                  section: name,
                  row: loungeName,
                  number: seatNumber,
                  category: category as SeatCategory,
                  isReserved: false,
                  isOccupied: false,
                  label: `${loungeName}-${seatNumber}`,
                  groupId: seatGroup.id,
                  venueType,
                },
              });

              createdSeats.push(seat);
            }
          }
          break;

        case 'theater':
          // Create theater-style seating
          for (let rowIndex = 0; rowIndex < theaterRows; rowIndex++) {
            const rowLabel = String.fromCharCode(65 + rowIndex); // A, B, C, ...

            for (let seatNumber = 1; seatNumber <= seatsPerTheaterRow; seatNumber++) {
              const seat = await db.seat.create({
                data: {
                  eventId,
                  section: name,
                  row: rowLabel,
                  number: seatNumber,
                  category: category as SeatCategory,
                  isReserved: false,
                  isOccupied: false,
                  label: `${rowLabel}${seatNumber}`,
                  venueType,
                },
              });

              createdSeats.push(seat);
            }
          }
          break;

        default:
          // Default to row-based seating
          for (let rowIndex = 0; rowIndex < rows; rowIndex++) {
            const rowLabel = String.fromCharCode(65 + rowIndex); // A, B, C, ...

            for (let seatNumber = 1; seatNumber <= seatsPerRow; seatNumber++) {
              const seat = await db.seat.create({
                data: {
                  eventId,
                  section: name,
                  row: rowLabel,
                  number: seatNumber,
                  category: category as SeatCategory,
                  isReserved: false,
                  isOccupied: false,
                },
              });

              createdSeats.push(seat);
            }
          }
      }
    }

    return NextResponse.json({
      message: 'Seating layout created successfully',
      seatsCreated: createdSeats.length,
    });
  } catch (error) {
    console.error('Error creating seating layout:', error);
    return NextResponse.json(
      { error: 'Failed to create seating layout' },
      { status: 500 }
    );
  }
}
