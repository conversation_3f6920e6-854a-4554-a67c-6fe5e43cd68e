import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';

/**
 * GET /api/events/[id]/seating/seats
 * Get seats for a specific section of an event
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
    const resolvedParams = await params;

  try {
    const eventId = resolvedParams.id;
    const { searchParams } = new URL(request.url);
    const section = searchParams.get('section');

    if (!section) {
      return NextResponse.json(
        { error: 'Section parameter is required' },
        { status: 400 }
      );
    }

    // Check if the event exists
    const event = await db.event.findUnique({
      where: { id: eventId },
      select: { id: true },
    });

    if (!event) {
      return NextResponse.json(
        { error: 'Event not found or you don\'t have permission to access it.' },
        { status: 404 }
      );
    }

    // Fetch seats for the specified section
    const seats = await db.seat.findMany({
      where: {
        eventId,
        section,
      },
      orderBy: [
        { row: 'asc' },
        { number: 'asc' },
      ],
    });

    return NextResponse.json(seats);
  } catch (error) {
    console.error('Error fetching seats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch seats' },
      { status: 500 }
    );
  }
}
