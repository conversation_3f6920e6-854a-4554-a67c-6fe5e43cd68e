import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * PATCH /api/events/[id]/seating/toggle
 * Toggle stadium seating for an event
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
    const resolvedParams = await params;

  try {
    const user = await currentUser();

    // Only allow authenticated users
    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const eventId = resolvedParams.id;
    const { hasStadiumSeating } = await request.json();

    // Check if the event exists and user has permission
    const event = await db.event.findUnique({
      where: { id: eventId },
      select: {
        id: true,
        userId: true,
      },
    });

    if (!event) {
      return NextResponse.json({
        error: 'Event not found or you don\'t have permission to access it.'
      }, { status: 404 });
    }

    // Check if user is authorized to modify this event
    const isOwner = event.userId === user.id;
    const isAdmin = user.role === 'ADMIN' || user.role === 'SUPERADMIN';

    if (!isOwner && !isAdmin) {
      return NextResponse.json(
        { error: 'You do not have permission to modify this event' },
        { status: 403 }
      );
    }

    // Update the event with the new hasStadiumSeating value
    const updatedEvent = await db.event.update({
      where: { id: eventId },
      data: {
        hasStadiumSeating: hasStadiumSeating === true,
      },
      select: {
        id: true,
        title: true,
        hasStadiumSeating: true,
        userId: true,
      },
    });

    return NextResponse.json({
      success: true,
      message: `Stadium seating ${updatedEvent.hasStadiumSeating ? 'enabled' : 'disabled'} successfully`,
      event: updatedEvent,
    });
  } catch (error) {
    console.error('Error toggling stadium seating:', error);
    return NextResponse.json(
      { error: 'Failed to toggle stadium seating' },
      { status: 500 }
    );
  }
}
