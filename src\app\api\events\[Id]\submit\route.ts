import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
    const resolvedParams = await params;

  try {
    console.log('Submit API called with params:', params);

    const user = await currentUser();
    console.log('Current user:', user ? `ID: ${user.id}, Role: ${user.role}` : 'No user');

    if (!user?.id) {
      console.log('Unauthorized: No user ID');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const eventId = resolvedParams.id;
    console.log('Event ID:', eventId);

    // Get the event
    console.log('Fetching event with ID:', eventId);
    try {
      const event = await db.event.findUnique({
        where: { id: eventId },
        select: {
          id: true,
          title: true,
          userId: true,
          status: true,
        },
      });
      console.log('Event found:', event);

      // Check if event exists
      if (!event) {
        console.log('Event not found');
        return NextResponse.json(
          { error: 'Event not found' },
          { status: 404 }
        );
      }

      // Check if user owns the event
      if (event.userId !== user.id) {
        console.log(`Permission denied: Event user ID (${event.userId}) doesn't match current user ID (${user.id})`);
        return NextResponse.json(
          { error: 'You do not have permission to submit this event' },
          { status: 403 }
        );
      }

      // Check if event is in Draft status
      console.log('Event status:', event.status);
      if (event.status !== 'Draft') {
        console.log(`Invalid status: Event is in ${event.status} status, not Draft`);
        return NextResponse.json(
          { error: `Event cannot be submitted because it is already in ${event.status} status` },
          { status: 400 }
        );
      }

      // Update event status to UnderReview
      console.log('Updating event status to UnderReview');
      try {
        // Import the EventStatus enum from Prisma
        const { EventStatus } = await import('@prisma/client');
        console.log('Available EventStatus values:', Object.values(EventStatus));

        const updatedEvent = await db.event.update({
          where: { id: eventId },
          data: { status: EventStatus.UnderReview },
        });
        console.log('Event updated successfully');
      } catch (updateError) {
        console.error('Error updating event status:', updateError);
        throw updateError;
      }
    } catch (dbError) {
      console.error('Database error:', dbError);
      throw dbError;
    }

    try {
      // Get the event title for notifications
      const eventDetails = await db.event.findUnique({
        where: { id: eventId },
        select: { title: true },
      });

      if (!eventDetails) {
        console.log('Event details not found for notifications');
        throw new Error('Event details not found');
      }

      console.log('Creating notification for user');
      // Create notification for the event owner
      await db.notification.create({
        data: {
          userId: user.id!,
          message: `Your event "${eventDetails.title}" has been submitted for review.`,
          type: 'EVENT_SUBMITTED',
          isRead: false,
        },
      });
      console.log('User notification created');

      // Find admin users
      console.log('Finding admin users');
      const adminUsers = await db.user.findMany({
        where: {
          OR: [
            { role: 'ADMIN' },
            { role: 'SUPERADMIN' }
          ]
        },
        select: { id: true }
      });
      console.log(`Found ${adminUsers.length} admin users`);

      // Create notifications for admins
      if (adminUsers.length > 0) {
        console.log('Creating admin notifications');
        for (const admin of adminUsers) {
          await db.notification.create({
            data: {
              userId: admin.id,
              message: `New event "${eventDetails.title}" has been submitted for review.`,
              type: 'EVENT_REVIEW_NEEDED',
              isRead: false,
            },
          });
        }
        console.log('Admin notifications created');
      }
    } catch (notificationError) {
      console.error('Error creating notifications:', notificationError);
      // Continue even if notifications fail
    }

    console.log('Returning success response');
    return NextResponse.json({
      success: true,
      message: 'Event submitted for review successfully',
      event: {
        id: eventId,
        status: 'UnderReview'
      }
    });
  } catch (error) {
    console.error('Error submitting event for review:', error);
    // Return more detailed error information
    const errorMessage = error instanceof Error
      ? `${error.name}: ${error.message}`
      : 'Unknown error';

    return NextResponse.json(
      {
        error: 'Failed to submit event for review',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
