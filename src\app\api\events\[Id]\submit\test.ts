// This is a test script to verify the API endpoint
// It's not meant to be executed directly, but to be used as a reference

import { PrismaClient, EventStatus } from '@prisma/client';

async function testEventSubmission() {
  const prisma = new PrismaClient();
  
  try {
    console.log('Available EventStatus values:', Object.values(EventStatus));
    
    // Get a draft event
    const draftEvent = await prisma.event.findFirst({
      where: { status: EventStatus.Draft },
      select: { id: true, title: true, status: true }
    });
    
    console.log('Draft event:', draftEvent);
    
    if (!draftEvent) {
      console.log('No draft events found');
      return;
    }
    
    // Update the event status
    const updatedEvent = await prisma.event.update({
      where: { id: draftEvent.id },
      data: { status: EventStatus.UnderReview },
      select: { id: true, title: true, status: true }
    });
    
    console.log('Updated event:', updatedEvent);
    
    // Reset the event status back to Draft
    const resetEvent = await prisma.event.update({
      where: { id: draftEvent.id },
      data: { status: EventStatus.Draft },
      select: { id: true, title: true, status: true }
    });
    
    console.log('Reset event:', resetEvent);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// This function is not called, it's just for reference
// testEventSubmission();
