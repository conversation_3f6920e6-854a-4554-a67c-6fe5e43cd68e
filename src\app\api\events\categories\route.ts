import { NextRequest, NextResponse } from 'next/server';
import { EventCategory } from '@prisma/client';
import { addCorsHeaders, corsPreflightResponse } from '@/lib/cors';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // Get all event categories from the enum
    const categories = Object.values(EventCategory);

    // Format categories for display
    const formattedCategories = categories.map(category => ({
      id: category,
      name: formatCategoryName(category),
    }));

    const response = NextResponse.json({
      categories: formattedCategories
    });

    // Add CORS headers using our utility function
    return addCorsHeaders(response, request);
  } catch (error) {
    console.error('Error fetching categories:', error);

    const errorResponse = NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );

    // Add CORS headers to error response
    return addCorsHeaders(errorResponse, request);
  }
}

// Helper function to format category names
function formatCategoryName(category: string): string {
  return category
    .replace(/_/g, ' ')
    .replace(/\b\w/g, char => char.toUpperCase());
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(request: NextRequest) {
  return corsPreflightResponse('GET, OPTIONS', request);
}
