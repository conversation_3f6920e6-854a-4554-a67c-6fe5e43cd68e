import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await currentUser();

    // Only allow authenticated users
    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: eventId } = await params;
    console.log('Checking event with ID:', eventId);

    // Get the event without status filter
    const event = await db.event.findUnique({
      where: { id: eventId },
      select: {
        id: true,
        title: true,
        status: true,
        userId: true,
      },
    });

    if (!event) {
      console.log('Event not found with ID:', eventId);
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }

    console.log('Event found:', event);

    // Check if user is authorized to see this event
    const isOwner = event.userId === user.id;
    const isAdmin = user.role === 'ADMIN' || user.role === 'SUPERADMIN';
    
    if (!isOwner && !isAdmin) {
      return NextResponse.json(
        { error: 'You do not have permission to view this event' },
        { status: 403 }
      );
    }

    return NextResponse.json({
      id: event.id,
      title: event.title,
      status: event.status,
      isOwner,
      isAdmin,
    });
  } catch (error) {
    console.error('Error checking event:', error);
    return NextResponse.json(
      { error: 'Failed to check event' },
      { status: 500 }
    );
  }
}
