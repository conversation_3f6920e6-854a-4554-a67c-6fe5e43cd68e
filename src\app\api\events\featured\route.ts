import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { EventCategory } from '@prisma/client';
import { getCurrentUser } from '@/lib/session';
import { addCorsHeaders, corsPreflightResponse } from '@/lib/cors';

export const dynamic = 'force-dynamic';

// Helper function to add full image URLs
function addImageUrls(events: any[], request: NextRequest) {
  const protocol = request.headers.get('x-forwarded-proto') || 'http';
  const host = request.headers.get('host') || 'localhost:3000';
  const baseUrl = `${protocol}://${host}`;

  return events.map(event => {
    if (event.imagePath) {
      // Add both the original path and a full URL
      return {
        ...event,
        imageUrl: `${baseUrl}/api/images${event.imagePath}`
      };
    }
    return event;
  });
}

/**
 * GET /api/events/featured
 * Get featured events
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const categoryParam = searchParams.get('category');
    const location = searchParams.get('location');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50); // Max 50 items

    // Validate category if provided
    let category: string | null = null;
    if (categoryParam && Object.values(EventCategory).includes(categoryParam as EventCategory)) {
      category = categoryParam;
    }

    // Get current date for filtering active featuring
    const now = new Date();

    // Query events with active promotions or featured metadata
    const events = await db.event.findMany({
      where: {
        status: 'Published',
        OR: [
          // Check for events with active promotions that are for featuring
          {
            promotions: {
              some: {
                // Use a more generic condition that matches promotion records
                // that are used for featuring events
                OR: [
                  { type: 'FEATURED' },
                  { type: 'PROMOTION' },
                  { type: 'SPONSORED' }
                ],
                status: 'Active',
                startDate: { lte: now },
                endDate: { gte: now },
              },
            },
          },
          // Check for events with EventPromotion relation
          {
            promotion: {
              startDate: { lte: now },
              endDate: { gte: now },
            },
          },
          // Check for events with isFeatured in metadata
          {
            metadata: {
              path: ['isFeatured'],
              equals: true,
            },
          },
        ],
        // Add filters if provided
        ...(category ? { category: category as EventCategory } : {}),
        ...(location ? { location: { contains: location, mode: 'insensitive' } } : {}),
      },
      select: {
        id: true,
        title: true,
        description: true,
        startDate: true,
        endDate: true,
        location: true,
        venue: true,
        category: true,
        eventType: true,
        imagePath: true,
        metadata: true,
        // Include relations
        user: {
          select: {
            name: true,
            image: true,
          },
        },
        // Include promotions for featured events
        promotions: {
          where: {
            OR: [
              { type: 'FEATURED' },
              { type: 'PROMOTION' },
              { type: 'SPONSORED' }
            ],
            status: 'Active',
            startDate: { lte: now },
            endDate: { gte: now },
          },
          take: 1,
        },
      },
      orderBy: [
        // Order by start date as a fallback
        { startDate: 'asc' },
      ],
      take: limit,
    });

    // Add image URLs
    const eventsWithImages = addImageUrls(events, request);

    // Format the response
    const formattedEvents = eventsWithImages.map(event => {
      // Get promotion information
      const promotion = event.promotions && event.promotions.length > 0 ? event.promotions[0] : null;

      // Get metadata for tier information if available
      const metadata = event.metadata || {};
      const featuredTier = metadata.featuredTier || 'BASIC';

      // Determine if the event is featured and get the tier
      const isFeatured = promotion || metadata.isFeatured;
      const tier = featuredTier;
      const endDate = promotion?.endDate || new Date(metadata.featuredUntil || '');

      return {
        id: event.id,
        title: event.title,
        description: event.description,
        startDate: event.startDate,
        endDate: event.endDate,
        location: event.location,
        venue: event.venue,
        category: event.category,
        eventType: event.eventType,
        imageUrl: event.imageUrl,
        organizer: event.user.name,
        featuring: isFeatured ? {
          tier: tier,
          endDate: endDate,
          // Add badge text based on tier
          badge: tier === 'ELITE' ? 'Elite' :
                 tier === 'PREMIUM' ? 'Premium' : 'Featured',
          // Add badge color based on tier
          badgeColor: tier === 'ELITE' ? 'gold' :
                      tier === 'PREMIUM' ? 'purple' : 'blue',
          isSimulation: metadata.isSimulatedFeaturing || false,
        } : null,
      };
    });

    const response = NextResponse.json({ events: formattedEvents });
    return addCorsHeaders(response, request);
  } catch (error) {
    console.error('Error fetching featured events:', error);
    const errorResponse = NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
    return addCorsHeaders(errorResponse, request);
  }
}

/**
 * OPTIONS /api/events/featured
 * Handle CORS preflight requests
 */
export async function OPTIONS(request: NextRequest) {
  return corsPreflightResponse('GET, OPTIONS', request);
}
