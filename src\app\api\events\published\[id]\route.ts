import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { addCorsHeaders, corsPreflightResponse } from '@/lib/cors';

export const dynamic = 'force-dynamic';

// Helper function to add full image URL
function addImageUrl(event: any, request: NextRequest) {
  if (!event || !event.imagePath) return event;

  const protocol = request.headers.get('x-forwarded-proto') || 'http';
  const host = request.headers.get('host') || 'localhost:3001';
  const baseUrl = `${protocol}://${host}`;

  return {
    ...event,
    imageUrl: `${baseUrl}/api/images${event.imagePath}`
  };
}

export async function GET(request: NextRequest) {
  try {
    // Extract the ID from the URL path
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const eventId = pathParts[pathParts.length - 1]; // Get the ID from the URL path

    const event = await db.event.findUnique({
      where: {
        id: eventId,
        status: 'Published',
      },
      include: {
        user: {
          select: {
            name: true,
            image: true,
          },
        },
        ageRestriction: true,
        ParkingManagement: true,
        seoSettings: true,
        socialSettings: true,
      },
    });

    if (!event) {
      const notFoundResponse = NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );

      // Add CORS headers to error response
      return addCorsHeaders(notFoundResponse, request);
    }

    // Add full image URL to event
    const eventWithImage = addImageUrl(event, request);

    const response = NextResponse.json(eventWithImage);

    // Add CORS headers
    return addCorsHeaders(response, request);
  } catch (error) {
    console.error('Error fetching event details:', error);

    const errorResponse = NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );

    // Add CORS headers to error response
    return addCorsHeaders(errorResponse, request);
  }
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(request: NextRequest) {
  return corsPreflightResponse('GET, OPTIONS', request);
}
