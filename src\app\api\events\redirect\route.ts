import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { generateEventUrl } from '@/lib/utils/events';

export const dynamic = 'force-dynamic';

/**
 * GET /api/events/redirect?id=[eventId]&returnUrl=[originalUrl]
 * <PERSON><PERSON> redirects from old event URLs to new SEO-friendly format
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const eventId = searchParams.get('id');
    const returnUrl = searchParams.get('returnUrl');

    if (!eventId) {
      return NextResponse.json(
        { error: 'Event ID is required' },
        { status: 400 }
      );
    }

    // Fetch the event to get the data needed for the new URL
    const event = await db.event.findUnique({
      where: { 
        id: eventId,
        status: 'Published'
      },
      select: {
        id: true,
        title: true,
        category: true,
      }
    });

    if (!event) {
      // Event not found, redirect to events listing page
      return NextResponse.redirect(new URL('/events', request.url), 301);
    }

    // Generate the new SEO-friendly URL
    const newUrl = generateEventUrl(event);
    const redirectUrl = new URL(newUrl, request.url);
    
    // Add any query parameters from the original request
    const originalParams = new URL(returnUrl || '', request.url).searchParams;
    originalParams.forEach((value, key) => {
      redirectUrl.searchParams.set(key, value);
    });

    // Perform 301 redirect to the new URL
    return NextResponse.redirect(redirectUrl, 301);

  } catch (error) {
    console.error('Error in event redirect:', error);
    
    // On error, redirect to events listing page
    return NextResponse.redirect(new URL('/events', request.url), 301);
  }
}
