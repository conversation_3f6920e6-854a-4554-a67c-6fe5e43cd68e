import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { EventCategory, EventType } from '@prisma/client';
import { addCorsHeaders, corsPreflightResponse } from '@/lib/cors';

export const dynamic = 'force-dynamic';

// Helper function to add full image URLs
function addImageUrls(events: any[], request: NextRequest) {
  const protocol = request.headers.get('x-forwarded-proto') || 'http';
  const host = request.headers.get('host') || 'localhost:3001';
  const baseUrl = `${protocol}://${host}`;

  return events.map(event => {
    if (event.imagePath) {
      // Add both the original path and a full URL
      return {
        ...event,
        imageUrl: `${baseUrl}/api/images${event.imagePath}`
      };
    }
    return event;
  });
}

// GET endpoint for searching events with query parameters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const location = searchParams.get('location');
    const category = searchParams.get('category');
    const eventType = searchParams.get('type');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Build the where clause
    const where: any = {
      status: 'Published',
    };

    // Add search conditions
    if (query) {
      where.OR = [
        { title: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } },
        { location: { contains: query, mode: 'insensitive' } },
        { venue: { contains: query, mode: 'insensitive' } },
      ];
    }

    // Add filters
    if (location) where.location = { contains: location, mode: 'insensitive' };
    if (category) where.category = category as EventCategory;
    if (eventType) where.eventType = eventType as EventType;

    const events = await db.event.findMany({
      where,
      include: {
        user: {
          select: {
            name: true,
            image: true,
          },
        },
        ageRestriction: true,
        ParkingManagement: true,
      },
      orderBy: {
        startDate: 'desc',
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    const total = await db.event.count({ where });

    // Add full image URLs to events
    const eventsWithImages = addImageUrls(events, request);

    const response = NextResponse.json({
      events: eventsWithImages,
      pagination: {
        total,
        pages: Math.ceil(total / limit),
        page,
        limit,
      },
    });

    // Add CORS headers using our utility function
    return addCorsHeaders(response, request);
  } catch (error) {
    console.error('Error searching events:', error);

    const errorResponse = NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );

    // Add CORS headers to error response
    return addCorsHeaders(errorResponse, request);
  }
}

// POST endpoint for more complex searches
export async function POST(request: NextRequest) {
  try {
    const { location, category, eventType, title, query, page = 1, limit = 10 } = await request.json();

    // Build the where clause
    const where: any = {
      status: 'Published',
    };

    // Add search conditions
    if (query) {
      where.OR = [
        { title: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } },
        { location: { contains: query, mode: 'insensitive' } },
        { venue: { contains: query, mode: 'insensitive' } },
      ];
    }

    // Add filters
    if (location) where.location = { contains: location, mode: 'insensitive' };
    if (category) where.category = category as EventCategory;
    if (eventType) where.eventType = eventType as EventType;
    if (title) where.title = { contains: title, mode: 'insensitive' };

    const events = await db.event.findMany({
      where,
      include: {
        user: {
          select: {
            name: true,
            image: true,
          },
        },
        ageRestriction: true,
        ParkingManagement: true,
      },
      orderBy: {
        startDate: 'desc',
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    const total = await db.event.count({ where });

    // Add full image URLs to events
    const eventsWithImages = addImageUrls(events, request);

    const response = NextResponse.json({
      events: eventsWithImages,
      pagination: {
        total,
        pages: Math.ceil(total / limit),
        page,
        limit,
      },
    });

    // Add CORS headers using our utility function
    return addCorsHeaders(response, request);
  } catch (error) {
    console.error('Error in search endpoint:', error);

    const errorResponse = NextResponse.json(
      { error: 'An error occurred while searching for events' },
      { status: 500 }
    );

    // Add CORS headers to error response
    return addCorsHeaders(errorResponse, request);
  }
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(request: NextRequest) {
  return corsPreflightResponse('GET, POST, OPTIONS', request);
}
