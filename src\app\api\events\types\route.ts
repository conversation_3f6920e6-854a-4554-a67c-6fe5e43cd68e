import { NextRequest, NextResponse } from 'next/server';
import { EventType } from '@prisma/client';
import { addCorsHeaders, corsPreflightResponse } from '@/lib/cors';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // Get all event types from the enum
    const types = Object.values(EventType);

    // Format types for display
    const formattedTypes = types.map(type => ({
      id: type,
      name: formatTypeName(type),
    }));

    const response = NextResponse.json({
      types: formattedTypes
    });

    // Add CORS headers using our utility function
    return addCorsHeaders(response, request);
  } catch (error) {
    console.error('Error fetching event types:', error);

    const errorResponse = NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );

    // Add CORS headers to error response
    return addCorsHeaders(errorResponse, request);
  }
}

// Helper function to format type names
function formatTypeName(type: string): string {
  return type
    .replace(/_/g, ' ')
    .replace(/\b\w/g, char => char.toUpperCase());
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(request: NextRequest) {
  return corsPreflightResponse('GET, OPTIONS', request);
}
