'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  RefreshCw,
  Filter,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Activity,
  AlertTriangle,
} from 'lucide-react';
import { format } from 'date-fns';
import { ActivityLog, ActivityLogFilters } from '@/types/user-management';

interface ActivityLogsProps {
  logs: ActivityLog[];
  loading: boolean;
  filters: ActivityLogFilters;
  onFiltersChange: (filters: ActivityLogFilters) => void;
  onRefresh: () => void;
  summary?: {
    totalLogs: number;
    successfulActions: number;
    failedActions: number;
    uniqueAdmins: number;
  };
}

export default function ActivityLogs({
  logs,
  loading,
  filters,
  onFiltersChange,
  onRefresh,
  summary,
}: ActivityLogsProps) {
  const [selectedLog, setSelectedLog] = useState<ActivityLog | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  const handleFilterChange = (key: keyof ActivityLogFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
      page: 1, // Reset to first page when filtering
    });
  };

  const getActionBadgeVariant = (action: string) => {
    if (action.includes('CREATE') || action.includes('ACCEPT')) return 'default';
    if (action.includes('UPDATE') || action.includes('CHANGE')) return 'secondary';
    if (action.includes('DELETE')) return 'destructive';
    if (action.includes('UNAUTHORIZED') || action.includes('FAILED')) return 'destructive';
    if (action.includes('VIEW') || action.includes('LOGIN_SUCCESS')) return 'outline';
    return 'outline';
  };

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const formatTimestamp = (timestamp: string) => {
    return format(new Date(timestamp), 'MMM dd, yyyy HH:mm:ss');
  };

  const formatAction = (action: string) => {
    return action.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-32 bg-gray-100 rounded animate-pulse" />
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-16 bg-gray-100 rounded animate-pulse" />
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Logs</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalLogs}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Successful</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {summary.successfulActions}
              </div>
              <p className="text-xs text-muted-foreground">
                {summary.totalLogs > 0 
                  ? `${((summary.successfulActions / summary.totalLogs) * 100).toFixed(1)}%`
                  : '0%'
                } success rate
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Failed</CardTitle>
              <XCircle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {summary.failedActions}
              </div>
              <p className="text-xs text-muted-foreground">
                {summary.totalLogs > 0 
                  ? `${((summary.failedActions / summary.totalLogs) * 100).toFixed(1)}%`
                  : '0%'
                } failure rate
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Admins</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.uniqueAdmins}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="mr-2 h-4 w-4" />
            Filters
          </Button>
          <Button variant="outline" size="sm" onClick={onRefresh}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filters</CardTitle>
            <CardDescription>
              Filter activity logs by action, resource, admin, or date range
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
              <div className="space-y-2">
                <Label htmlFor="action-filter">Action</Label>
                <Input
                  id="action-filter"
                  placeholder="Filter by action..."
                  value={filters.action || ''}
                  onChange={(e) => handleFilterChange('action', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="resource-filter">Resource</Label>
                <Input
                  id="resource-filter"
                  placeholder="Filter by resource..."
                  value={filters.resource || ''}
                  onChange={(e) => handleFilterChange('resource', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="success-filter">Status</Label>
                <Select
                  value={filters.success === null ? 'all' : filters.success.toString()}
                  onValueChange={(value) => 
                    handleFilterChange('success', value === 'all' ? null : value === 'true')
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="true">Success</SelectItem>
                    <SelectItem value="false">Failed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="start-date">Start Date</Label>
                <Input
                  id="start-date"
                  type="date"
                  value={filters.startDate || ''}
                  onChange={(e) => handleFilterChange('startDate', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="end-date">End Date</Label>
                <Input
                  id="end-date"
                  type="date"
                  value={filters.endDate || ''}
                  onChange={(e) => handleFilterChange('endDate', e.target.value)}
                />
              </div>
            </div>
            <div className="mt-4 flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onFiltersChange({})}
              >
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Activity Logs Table */}
      <Card>
        <CardHeader>
          <CardTitle>Activity Logs</CardTitle>
          <CardDescription>
            Recent administrative actions and system events
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Timestamp</TableHead>
                  <TableHead>Admin</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Resource</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>IP Address</TableHead>
                  <TableHead className="text-right">Details</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {logs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center space-y-2">
                        <Activity className="h-8 w-8 text-gray-400" />
                        <p className="text-gray-500">No activity logs found</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  logs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">
                            {formatTimestamp(log.timestamp)}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {log.admin ? (
                            <div>
                              <div className="font-medium">
                                {log.admin.name || 'Anonymous'}
                              </div>
                              <div className="text-gray-500">
                                {log.admin.email}
                              </div>
                            </div>
                          ) : (
                            <span className="text-gray-500">System</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getActionBadgeVariant(log.action)}>
                          {formatAction(log.action)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">
                          {log.resource || 'N/A'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(log.success)}
                          <span className="text-sm">
                            {log.success ? 'Success' : 'Failed'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-gray-500">
                          {log.ipAddress || 'Unknown'}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Activity Log Details</DialogTitle>
                              <DialogDescription>
                                Detailed information about this activity
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <Label>Timestamp</Label>
                                  <p className="text-sm">{formatTimestamp(log.timestamp)}</p>
                                </div>
                                <div>
                                  <Label>Status</Label>
                                  <div className="flex items-center space-x-2">
                                    {getStatusIcon(log.success)}
                                    <span className="text-sm">
                                      {log.success ? 'Success' : 'Failed'}
                                    </span>
                                  </div>
                                </div>
                                <div>
                                  <Label>Action</Label>
                                  <p className="text-sm">{formatAction(log.action)}</p>
                                </div>
                                <div>
                                  <Label>Resource</Label>
                                  <p className="text-sm">{log.resource || 'N/A'}</p>
                                </div>
                                <div>
                                  <Label>IP Address</Label>
                                  <p className="text-sm">{log.ipAddress || 'Unknown'}</p>
                                </div>
                                <div>
                                  <Label>User Agent</Label>
                                  <p className="text-sm text-gray-500 truncate">
                                    {log.userAgent || 'Unknown'}
                                  </p>
                                </div>
                              </div>
                              {log.errorMessage && (
                                <div>
                                  <Label>Error Message</Label>
                                  <div className="bg-red-50 border border-red-200 rounded p-3">
                                    <p className="text-sm text-red-700">{log.errorMessage}</p>
                                  </div>
                                </div>
                              )}
                              {log.details && (
                                <div>
                                  <Label>Additional Details</Label>
                                  <div className="bg-gray-50 border rounded p-3">
                                    <pre className="text-xs overflow-auto">
                                      {JSON.stringify(log.details, null, 2)}
                                    </pre>
                                  </div>
                                </div>
                              )}
                            </div>
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
