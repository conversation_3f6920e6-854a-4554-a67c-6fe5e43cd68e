import { db } from '@/lib/prisma';
import { NextRequest } from 'next/server';

export interface ActivityLogData {
  userId?: string;
  adminId?: string;
  action: string;
  resource?: string;
  resourceId?: string;
  details?: any;
  success?: boolean;
  errorMessage?: string;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Log an activity to the database
 */
export async function logActivity(data: ActivityLogData): Promise<void> {
  try {
    await db.activityLog.create({
      data: {
        userId: data.userId || null,
        adminId: data.adminId || null,
        action: data.action,
        resource: data.resource || null,
        resourceId: data.resourceId || null,
        details: data.details || null,
        success: data.success ?? true,
        errorMessage: data.errorMessage || null,
        ipAddress: data.ipAddress || null,
        userAgent: data.userAgent || null,
      },
    });
  } catch (error) {
    // Don't throw errors for logging failures to avoid breaking the main flow
    console.error('Failed to log activity:', error);
  }
}

/**
 * Extract IP address and user agent from request
 */
export function extractRequestInfo(request: NextRequest): {
  ipAddress: string;
  userAgent: string;
} {
  const ipAddress = 
    request.headers.get('x-forwarded-for') ||
    request.headers.get('x-real-ip') ||
    request.headers.get('cf-connecting-ip') ||
    'unknown';

  const userAgent = request.headers.get('user-agent') || 'unknown';

  return { ipAddress, userAgent };
}

/**
 * Log user authentication attempts
 */
export async function logLoginAttempt(
  email: string,
  success: boolean,
  userId?: string,
  failureReason?: string,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  try {
    // Log to LoginAttempt table
    await db.loginAttempt.create({
      data: {
        email,
        userId: userId || null,
        success,
        failureReason: failureReason || null,
        ipAddress: ipAddress || null,
        userAgent: userAgent || null,
      },
    });

    // Also log to ActivityLog for admin visibility
    await logActivity({
      userId,
      action: success ? 'LOGIN_SUCCESS' : 'LOGIN_FAILED',
      resource: 'authentication',
      details: {
        email,
        failureReason: success ? undefined : failureReason,
      },
      success,
      errorMessage: success ? undefined : failureReason,
      ipAddress,
      userAgent,
    });
  } catch (error) {
    console.error('Failed to log login attempt:', error);
  }
}

/**
 * Log user actions automatically based on API endpoints
 */
export function getActionFromEndpoint(method: string, pathname: string): string {
  const segments = pathname.split('/').filter(Boolean);
  
  // Admin API endpoints
  if (segments.includes('admin')) {
    if (segments.includes('users')) {
      switch (method) {
        case 'GET':
          return 'VIEW_USERS';
        case 'POST':
          return 'CREATE_USER';
        case 'PATCH':
          return 'UPDATE_USER';
        case 'DELETE':
          return 'DELETE_USER';
      }
    }
    
    if (segments.includes('activity-logs')) {
      return 'VIEW_ACTIVITY_LOGS';
    }
    
    if (segments.includes('invite')) {
      return 'INVITE_USER';
    }
  }
  
  // Auth endpoints
  if (segments.includes('auth')) {
    if (segments.includes('login')) {
      return 'LOGIN_ATTEMPT';
    }
    if (segments.includes('register')) {
      return 'REGISTER_ATTEMPT';
    }
    if (segments.includes('accept-invitation')) {
      return 'ACCEPT_INVITATION';
    }
    if (segments.includes('reset-password')) {
      return 'PASSWORD_RESET';
    }
  }
  
  // Event endpoints
  if (segments.includes('events')) {
    switch (method) {
      case 'GET':
        return 'VIEW_EVENTS';
      case 'POST':
        return 'CREATE_EVENT';
      case 'PATCH':
        return 'UPDATE_EVENT';
      case 'DELETE':
        return 'DELETE_EVENT';
    }
  }
  
  // Order endpoints
  if (segments.includes('orders')) {
    switch (method) {
      case 'GET':
        return 'VIEW_ORDERS';
      case 'POST':
        return 'CREATE_ORDER';
      case 'PATCH':
        return 'UPDATE_ORDER';
      case 'DELETE':
        return 'DELETE_ORDER';
    }
  }
  
  // Default action
  return `${method}_${segments.join('_').toUpperCase()}`;
}

/**
 * Get resource type from endpoint
 */
export function getResourceFromEndpoint(pathname: string): string {
  const segments = pathname.split('/').filter(Boolean);
  
  if (segments.includes('users')) return 'users';
  if (segments.includes('events')) return 'events';
  if (segments.includes('orders')) return 'orders';
  if (segments.includes('products')) return 'products';
  if (segments.includes('activity-logs')) return 'activity_logs';
  if (segments.includes('invite')) return 'user_invitations';
  if (segments.includes('auth')) return 'authentication';
  if (segments.includes('settings')) return 'settings';
  
  return 'unknown';
}

/**
 * Middleware helper to log API requests
 */
export async function logApiRequest(
  request: NextRequest,
  userId?: string,
  adminId?: string,
  success: boolean = true,
  errorMessage?: string,
  additionalDetails?: any
): Promise<void> {
  const { ipAddress, userAgent } = extractRequestInfo(request);
  const action = getActionFromEndpoint(request.method, request.nextUrl.pathname);
  const resource = getResourceFromEndpoint(request.nextUrl.pathname);
  
  await logActivity({
    userId,
    adminId,
    action,
    resource,
    details: {
      method: request.method,
      pathname: request.nextUrl.pathname,
      searchParams: Object.fromEntries(request.nextUrl.searchParams),
      ...additionalDetails,
    },
    success,
    errorMessage,
    ipAddress,
    userAgent,
  });
}

/**
 * Log user registration
 */
export async function logUserRegistration(
  userId: string,
  email: string,
  role: string,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await logActivity({
    userId,
    action: 'USER_REGISTRATION',
    resource: 'users',
    resourceId: userId,
    details: {
      email,
      role,
    },
    ipAddress,
    userAgent,
  });
}

/**
 * Log password changes
 */
export async function logPasswordChange(
  userId: string,
  isReset: boolean = false,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await logActivity({
    userId,
    action: isReset ? 'PASSWORD_RESET' : 'PASSWORD_CHANGE',
    resource: 'authentication',
    resourceId: userId,
    details: {
      type: isReset ? 'reset' : 'change',
    },
    ipAddress,
    userAgent,
  });
}

/**
 * Log role changes
 */
export async function logRoleChange(
  adminId: string,
  targetUserId: string,
  oldRole: string,
  newRole: string,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await logActivity({
    adminId,
    action: 'ROLE_CHANGE',
    resource: 'users',
    resourceId: targetUserId,
    details: {
      oldRole,
      newRole,
      targetUserId,
    },
    ipAddress,
    userAgent,
  });
}

/**
 * Log security events
 */
export async function logSecurityEvent(
  eventType: 'SUSPICIOUS_LOGIN' | 'ACCOUNT_LOCKED' | 'UNAUTHORIZED_ACCESS' | 'PERMISSION_DENIED',
  userId?: string,
  details?: any,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await logActivity({
    userId,
    action: eventType,
    resource: 'security',
    details,
    success: false,
    errorMessage: `Security event: ${eventType}`,
    ipAddress,
    userAgent,
  });
}

/**
 * Log data export/download activities
 */
export async function logDataExport(
  userId: string,
  dataType: string,
  recordCount?: number,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await logActivity({
    userId,
    action: 'DATA_EXPORT',
    resource: dataType,
    details: {
      dataType,
      recordCount,
      exportedAt: new Date().toISOString(),
    },
    ipAddress,
    userAgent,
  });
}

/**
 * Log system configuration changes
 */
export async function logConfigurationChange(
  adminId: string,
  configType: string,
  changes: any,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await logActivity({
    adminId,
    action: 'CONFIGURATION_CHANGE',
    resource: 'settings',
    details: {
      configType,
      changes,
    },
    ipAddress,
    userAgent,
  });
}
