import { Resend } from 'resend';

// Initialize Resend with API key
// In a real app, this would be stored in an environment variable
const resend = new Resend(process.env.RESEND_API_KEY || 'your_resend_api_key');

interface EmailAttachment {
  filename: string;
  content: Buffer;
}

interface SendEmailOptions {
  to: string;
  subject: string;
  html: string;
  attachments?: EmailAttachment[];
}

export async function sendPartnerRegistrationEmail(
  businessName: string,
  email: string,
  password: string
): Promise<{ success: boolean; error?: string; previewUrl?: string }> {
  try {
    // Generate the HTML email content
    const html = generatePartnerRegistrationEmailHtml(businessName, email, password);

    // Send the email
    const result = await sendEmail({
      to: email,
      subject: 'Welcome to QuickTime Events Partner Program - Your Account Details',
      html,
    });

    // Return the result
    return result;
  } catch (error) {
    console.error('Error sending partner registration email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred while sending partner registration email'
    };
  }
}

export async function sendUserInvitationEmail(
  email: string,
  name: string,
  password: string,
  role: string,
  invitationToken: string
): Promise<{ success: boolean; error?: string; previewUrl?: string }> {
  try {
    // Generate the HTML email content
    const html = generateUserInvitationEmailHtml(name, email, password, role, invitationToken);

    // Send the email
    const result = await sendEmail({
      to: email,
      subject: 'You\'ve been invited to join QuickTime Events Platform',
      html,
    });

    // Return the result
    return result;
  } catch (error) {
    console.error('Error sending user invitation email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred while sending user invitation email'
    };
  }
}

export async function sendEmail({ to, subject, html, attachments }: SendEmailOptions): Promise<{ success: boolean; error?: string; previewUrl?: string }> {
  try {
    // Validate email fields
    if (!to || typeof to !== 'string') {
      return { success: false, error: 'Invalid recipient email address' };
    }

    if (!subject || typeof subject !== 'string') {
      return { success: false, error: 'Email subject is required' };
    }

    if (!html || typeof html !== 'string') {
      return { success: false, error: 'Email HTML content is required' };
    }

    // Validate email format using a simple regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(to)) {
      return { success: false, error: 'Invalid email format' };
    }

    console.log(`Attempting to send email to: ${to}`);
    console.log(`Using Resend API key: ${process.env.RESEND_API_KEY ? 'API key exists' : 'API key missing'}`);

    const { data, error } = await resend.emails.send({
      from: '<EMAIL>',
      to,
      subject,
      html,
      attachments: attachments?.map(attachment => ({
        filename: attachment.filename,
        content: attachment.content,
      })),
    });

    if (error) {
      console.error('Error sending email:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      return { success: false, error: error.message };
    }

    console.log('Email sent successfully:', data);

    // For development, create a preview URL (this is just a placeholder since Resend doesn't provide this)
    let previewUrl;
    if (process.env.NODE_ENV !== 'production') {
      previewUrl = `https://resend.dev/email/${data?.id}`;
      console.log('Email preview URL (placeholder):', previewUrl);
    }

    return {
      success: true,
      previewUrl
    };
  } catch (error) {
    console.error('Exception caught while sending email:', error);
    if (error instanceof Error) {
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    } else {
      console.error('Unknown error type:', typeof error);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred while sending email'
    };
  }
}

export function generatePartnerRegistrationEmailHtml(
  businessName: string,
  email: string,
  password: string
): string {
  return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to QuickTime Events Partner Program</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    body {
      font-family: 'Inter', sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f5f7fa;
    }

    .container {
      max-width: 600px;
      margin: 20px auto;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 10px 25px rgba(0,0,0,0.08);
    }

    .header {
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      padding: 40px 20px;
      text-align: center;
      position: relative;
    }

    .header h1 {
      color: white;
      margin: 0;
      font-weight: 700;
      font-size: 28px;
      letter-spacing: -0.5px;
    }

    .header::after {
      content: "";
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 20px;
      background-color: white;
      border-radius: 100% 100% 0 0;
    }

    .content {
      padding: 30px;
      background-color: #ffffff;
    }

    .greeting {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
    }

    .credentials-box {
      margin: 25px 0;
      padding: 20px;
      background-color: #f8fafc;
      border-radius: 12px;
      border-left: 4px solid #8b5cf6;
    }

    .credentials-box h3 {
      margin-top: 0;
      color: #1e293b;
      font-size: 18px;
    }

    .credential-row {
      display: flex;
      justify-content: space-between;
      margin: 12px 0;
      align-items: center;
    }

    .credential-label {
      font-weight: 600;
      color: #64748b;
      font-size: 14px;
    }

    .credential-value {
      font-weight: 500;
      color: #1e293b;
      font-size: 16px;
      font-family: monospace;
      background-color: #f1f5f9;
      padding: 4px 8px;
      border-radius: 4px;
    }

    .button {
      display: inline-block;
      padding: 12px 30px;
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      color: #ffffff;
      text-decoration: none;
      border-radius: 8px;
      font-weight: 600;
      margin-top: 25px;
      text-align: center;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
    }

    .button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(139, 92, 246, 0.3);
    }

    .divider {
      height: 1px;
      background-color: #e2e8f0;
      margin: 25px 0;
    }

    .signature {
      margin-top: 25px;
      font-weight: 500;
    }

    .company-name {
      font-weight: 600;
      color: #8b5cf6;
    }

    .footer {
      margin-top: 30px;
      text-align: center;
      font-size: 13px;
      color: #94a3b8;
      padding: 0 20px 30px;
    }

    .security-note {
      background-color: #fffbeb;
      border-left: 4px solid #f59e0b;
      padding: 15px;
      margin: 25px 0;
      border-radius: 8px;
    }

    .security-note h4 {
      margin-top: 0;
      color: #92400e;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Welcome to Our Partner Program!</h1>
    </div>
    <div class="content">
      <p class="greeting">Hello ${businessName},</p>
      <p>Thank you for registering as a partner with QuickTime Events! Your account has been created successfully.</p>

      <div class="credentials-box">
        <h3>Your Login Credentials</h3>
        <div class="credential-row">
          <span class="credential-label">EMAIL</span>
          <span class="credential-value">${email}</span>
        </div>
        <div class="credential-row">
          <span class="credential-label">TEMPORARY PASSWORD</span>
          <span class="credential-value">${password}</span>
        </div>
      </div>

      <div class="security-note">
        <h4>Important Security Information</h4>
        <p>For security reasons, we strongly recommend changing your password after your first login. Your temporary password should be kept confidential.</p>
      </div>

      <p>As a partner, you'll be able to:</p>
      <ul>
        <li>Manage your business profile</li>
        <li>Create and publish promotions</li>
        <li>Access NFC payment features</li>
        <li>View analytics and reports</li>
      </ul>

      <a href="http://localhost:3000/login" class="button">Log In Now</a>

      <div class="divider"></div>

      <p class="signature">Best regards,<br><span class="company-name">The QuickTime Events Team</span></p>
    </div>
    <div class="footer">
      <p>This is an automated email. Please do not reply to this message.</p>
      <p>&copy; 2024 QuickTime Events. All rights reserved.</p>
    </div>
  </div>
</body>
</html>
  `;
}

export function generateTicketEmailHtml(
  recipientName: string,
  eventName: string,
  ticketType: string,
  eventDate: string,
  eventLocation: string,
  ticketId: string = 'TICKET-ID'
): string {
  // Validate required fields
  if (!recipientName || typeof recipientName !== 'string') {
    recipientName = 'Valued Customer';
  }

  if (!eventName || typeof eventName !== 'string') {
    eventName = 'Event';
  }

  if (!ticketType || typeof ticketType !== 'string') {
    ticketType = 'Standard';
  }

  if (!eventDate || typeof eventDate !== 'string') {
    eventDate = 'Please check event details';
  }

  if (!eventLocation || typeof eventLocation !== 'string') {
    eventLocation = 'Please check event details';
  }

  if (!ticketId || typeof ticketId !== 'string') {
    ticketId = 'TICKET-ID';
  }
  return `
   <!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Your Ticket for ${eventName}</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    body {
      font-family: 'Inter', sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f5f7fa;
    }

    .container {
      max-width: 600px;
      margin: 20px auto;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 10px 25px rgba(0,0,0,0.08);
    }

    .header {
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      padding: 40px 20px;
      text-align: center;
      position: relative;
    }

    .header h1 {
      color: white;
      margin: 0;
      font-weight: 700;
      font-size: 28px;
      letter-spacing: -0.5px;
    }

    .header::after {
      content: "";
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 20px;
      background-color: white;
      border-radius: 100% 100% 0 0;
    }

    .event-image {
      width: 100%;
      max-height: 200px;
      object-fit: cover;
      object-position: center;
      display: block;
    }

    .content {
      padding: 30px;
      background-color: #ffffff;
    }

    .greeting {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
    }

    .ticket-details {
      margin: 25px 0;
      padding: 20px;
      background-color: #f8fafc;
      border-radius: 12px;
      border-left: 4px solid #8b5cf6;
    }

    .qr-code-section {
      margin: 25px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #f8fafc;
      border-radius: 12px;
      overflow: hidden;
    }

    .qr-code-container {
      padding: 20px;
      text-align: center;
      width: 35%;
      background-color: white;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .qr-code-image {
      width: 100%;
      max-width: 150px;
      height: auto;
    }

    .qr-text {
      font-size: 12px;
      color: #64748b;
      margin-top: 10px;
      font-weight: 500;
    }

    .qr-info {
      padding: 20px;
      width: 65%;
    }

    .qr-info h3, .qr-info-multiple h3 {
      margin-top: 0;
      color: #1e293b;
      font-size: 16px;
    }

    .qr-info-multiple {
      margin: 25px 0;
      padding: 20px;
      background-color: #f0f9ff;
      border-radius: 12px;
      border-left: 4px solid #0ea5e9;
    }

    .ticket-details .detail-row {
      display: flex;
      justify-content: space-between;
      margin: 12px 0;
      align-items: center;
    }

    .ticket-details .detail-label {
      font-weight: 600;
      color: #64748b;
      font-size: 14px;
    }

    .ticket-details .detail-value {
      font-weight: 500;
      color: #1e293b;
      font-size: 16px;
    }

    .button {
      display: inline-block;
      padding: 12px 30px;
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      color: #ffffff;
      text-decoration: none;
      border-radius: 8px;
      font-weight: 600;
      margin-top: 25px;
      text-align: center;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
    }

    .button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(139, 92, 246, 0.3);
    }

    .divider {
      height: 1px;
      background-color: #e2e8f0;
      margin: 25px 0;
    }

    .signature {
      margin-top: 25px;
      font-weight: 500;
    }

    .company-name {
      font-weight: 600;
      color: #8b5cf6;
    }

    .footer {
      margin-top: 30px;
      text-align: center;
      font-size: 13px;
      color: #94a3b8;
      padding: 0 20px 30px;
    }

    @media (max-width: 600px) {
      .qr-code-section {
        flex-direction: column;
      }

      .qr-code-container, .qr-info {
        width: 100%;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Your Ticket is Ready!</h1>
    </div>
    <img src="/api/placeholder/600/200" alt="${eventName}" class="event-image" />
    <div class="content">
      <p class="greeting">Hello ${recipientName},</p>
      <p>Thank you for your purchase. Your ticket${ticketType.includes(',') ? 's' : ''} for <strong>${eventName}</strong> ${ticketType.includes(',') ? 'are' : 'is'} attached to this email as PDF file${ticketType.includes(',') ? 's' : ''}.</p>

      <div class="ticket-details">
        <div class="detail-row">
          <span class="detail-label">EVENT</span>
          <span class="detail-value">${eventName}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">TICKET TYPE</span>
          <span class="detail-value">${ticketType.includes(',') ? 'Multiple Types' : ticketType}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">DATE</span>
          <span class="detail-value">${eventDate}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">LOCATION</span>
          <span class="detail-value">${eventLocation}</span>
        </div>
      </div>

      ${ticketType.includes(',') ? `
      <div class="qr-info-multiple">
        <h3>Multiple Tickets Included</h3>
        <p>You have purchased multiple tickets. Each PDF attachment contains an individual ticket with its own unique QR code for entry.</p>
        <p>Order ID: ${ticketId}</p>
      </div>
      ` : `
      <div class="qr-code-section">
        <div class="qr-code-container">
          <img src="/api/placeholder/150/150" alt="QR Code" class="qr-code-image">
          <p class="qr-text">Ticket ID: ${ticketId}</p>
        </div>
        <div class="qr-info">
          <h3>Scan for entry</h3>
          <p>Present this QR code at the event entrance for quick check-in. The code contains your unique ticket information.</p>
        </div>
      </div>
      `}

      <p>We look forward to seeing you at the event!</p>

      <a href="#" class="button">View Your Ticket${ticketType.includes(',') ? 's' : ''}</a>

      <div class="divider"></div>

      <p class="signature">Best regards,<br><span class="company-name">The Event Team</span></p>
    </div>
    <div class="footer">
      <p>This is an automated email. Please do not reply to this message.</p>
      <p>&copy; 2024 Your Event Platform. All rights reserved.</p>
    </div>
  </div>
</body>
</html>

  `;
}

export function generateUserInvitationEmailHtml(
  name: string,
  email: string,
  password: string,
  role: string,
  invitationToken: string
): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  const acceptUrl = `${baseUrl}/auth/accept-invitation?token=${invitationToken}`;

  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>You've been invited to QuickTime Events</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f4f4f4;
    }
    .container {
      background-color: #ffffff;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }
    .header h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 300;
    }
    .content {
      padding: 40px 30px;
    }
    .greeting {
      font-size: 18px;
      margin-bottom: 20px;
      color: #2c3e50;
    }
    .invitation-details {
      background-color: #f8f9fa;
      border-left: 4px solid #667eea;
      padding: 20px;
      margin: 25px 0;
      border-radius: 5px;
    }
    .detail-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      padding: 8px 0;
      border-bottom: 1px solid #e9ecef;
    }
    .detail-row:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
    .detail-label {
      font-weight: 600;
      color: #495057;
      text-transform: uppercase;
      font-size: 12px;
      letter-spacing: 0.5px;
    }
    .detail-value {
      color: #2c3e50;
      font-weight: 500;
    }
    .credentials-box {
      background-color: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 8px;
      padding: 20px;
      margin: 25px 0;
      text-align: center;
    }
    .credentials-box h3 {
      margin-top: 0;
      color: #856404;
    }
    .password {
      font-family: 'Courier New', monospace;
      font-size: 18px;
      font-weight: bold;
      color: #d63031;
      background-color: #fff;
      padding: 10px;
      border-radius: 5px;
      border: 2px dashed #ffeaa7;
      margin: 10px 0;
    }
    .button {
      display: inline-block;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 15px 30px;
      text-decoration: none;
      border-radius: 25px;
      font-weight: 600;
      text-align: center;
      margin: 20px 0;
      transition: transform 0.2s ease;
    }
    .button:hover {
      transform: translateY(-2px);
    }
    .security-note {
      background-color: #e8f4fd;
      border-left: 4px solid #3498db;
      padding: 15px;
      margin: 25px 0;
      border-radius: 5px;
    }
    .security-note h4 {
      margin-top: 0;
      color: #2980b9;
    }
    .footer {
      background-color: #f8f9fa;
      padding: 20px 30px;
      text-align: center;
      color: #6c757d;
      font-size: 14px;
    }
    .divider {
      height: 1px;
      background-color: #e9ecef;
      margin: 30px 0;
    }
    .company-name {
      font-weight: 600;
      color: #667eea;
    }
    .role-badge {
      display: inline-block;
      background-color: #667eea;
      color: white;
      padding: 4px 12px;
      border-radius: 15px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Welcome to QuickTime Events!</h1>
    </div>
    <div class="content">
      <p class="greeting">Hello ${name},</p>
      <p>You've been invited to join the QuickTime Events platform! An administrator has created an account for you with the following details:</p>

      <div class="invitation-details">
        <div class="detail-row">
          <span class="detail-label">Email Address</span>
          <span class="detail-value">${email}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Role</span>
          <span class="detail-value"><span class="role-badge">${role}</span></span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Account Status</span>
          <span class="detail-value">Pending Activation</span>
        </div>
      </div>

      <div class="credentials-box">
        <h3>🔐 Your Temporary Password</h3>
        <p>Use this password to log in for the first time:</p>
        <div class="password">${password}</div>
        <p><strong>Important:</strong> Please change this password after your first login for security.</p>
      </div>

      <div style="text-align: center;">
        <a href="${acceptUrl}" class="button">Accept Invitation & Login</a>
      </div>

      <div class="security-note">
        <h4>🛡️ Security Information</h4>
        <ul style="margin: 0; padding-left: 20px;">
          <li>This invitation will expire in 7 days</li>
          <li>Change your password immediately after first login</li>
          <li>Never share your login credentials with anyone</li>
          <li>Contact support if you didn't expect this invitation</li>
        </ul>
      </div>

      <div class="divider"></div>

      <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>

      <p class="signature">Best regards,<br><span class="company-name">The QuickTime Events Team</span></p>
    </div>
    <div class="footer">
      <p>This invitation was sent to ${email}. If you didn't expect this invitation, please ignore this email.</p>
      <p>&copy; 2024 QuickTime Events Platform. All rights reserved.</p>
    </div>
  </div>
</body>
</html>
  `;
}
