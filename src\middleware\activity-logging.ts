import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { logApiRequest, extractRequestInfo } from '@/lib/activity-logger';

/**
 * Middleware to automatically log API requests
 */
export async function activityLoggingMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Skip logging for certain endpoints to avoid noise
  const skipLogging = [
    '/api/auth/session',
    '/api/auth/csrf',
    '/api/auth/providers',
    '/api/health',
    '/api/ping',
    '/_next',
    '/favicon.ico',
    '/api/auth/callback',
  ];

  const shouldSkip = skipLogging.some(path => pathname.startsWith(path));
  if (shouldSkip) {
    return NextResponse.next();
  }

  // Only log API requests
  if (!pathname.startsWith('/api/')) {
    return NextResponse.next();
  }

  const startTime = Date.now();
  let userId: string | undefined;
  let adminId: string | undefined;
  let success = true;
  let errorMessage: string | undefined;

  try {
    // Get user information from JWT token
    const token = await getToken({ req: request });
    if (token?.sub) {
      userId = token.sub;
      
      // Check if user has admin privileges
      const userRole = token.role as string;
      if (['ADMIN', 'SUPERADMIN', 'DEVELOPER'].includes(userRole)) {
        adminId = token.sub;
      }
    }

    // Continue with the request
    const response = NextResponse.next();

    // Log successful request
    const duration = Date.now() - startTime;
    
    // Don't await to avoid slowing down the response
    logApiRequest(request, userId, adminId, true, undefined, {
      duration,
      statusCode: response.status,
    }).catch(error => {
      console.error('Failed to log API request:', error);
    });

    return response;

  } catch (error) {
    success = false;
    errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    // Log failed request
    const duration = Date.now() - startTime;
    
    logApiRequest(request, userId, adminId, false, errorMessage, {
      duration,
      error: errorMessage,
    }).catch(logError => {
      console.error('Failed to log API request error:', logError);
    });

    // Re-throw the error to maintain normal error handling
    throw error;
  }
}

/**
 * Enhanced logging for specific admin actions
 */
export async function logAdminAction(
  request: NextRequest,
  action: string,
  resource: string,
  resourceId?: string,
  details?: any,
  success: boolean = true,
  errorMessage?: string
) {
  try {
    const token = await getToken({ req: request });
    const { ipAddress, userAgent } = extractRequestInfo(request);
    
    if (!token?.sub) {
      console.warn('Attempted to log admin action without valid token');
      return;
    }

    const { logActivity } = await import('@/lib/activity-logger');
    
    await logActivity({
      adminId: token.sub,
      action,
      resource,
      resourceId,
      details,
      success,
      errorMessage,
      ipAddress,
      userAgent,
    });
  } catch (error) {
    console.error('Failed to log admin action:', error);
  }
}

/**
 * Log authentication events
 */
export async function logAuthEvent(
  request: NextRequest,
  event: 'LOGIN_SUCCESS' | 'LOGIN_FAILED' | 'LOGOUT' | 'REGISTRATION',
  email?: string,
  userId?: string,
  errorMessage?: string
) {
  try {
    const { ipAddress, userAgent } = extractRequestInfo(request);
    const { logActivity } = await import('@/lib/activity-logger');
    
    await logActivity({
      userId,
      action: event,
      resource: 'authentication',
      details: {
        email,
        timestamp: new Date().toISOString(),
      },
      success: !errorMessage,
      errorMessage,
      ipAddress,
      userAgent,
    });
  } catch (error) {
    console.error('Failed to log auth event:', error);
  }
}

/**
 * Wrapper for API route handlers to automatically log activities
 */
export function withActivityLogging<T extends any[]>(
  handler: (...args: T) => Promise<NextResponse>,
  options?: {
    action?: string;
    resource?: string;
    skipLogging?: boolean;
  }
) {
  return async (...args: T): Promise<NextResponse> => {
    const request = args[0] as NextRequest;
    const startTime = Date.now();
    
    if (options?.skipLogging) {
      return handler(...args);
    }

    try {
      const response = await handler(...args);
      
      // Log successful request
      if (options?.action && options?.resource) {
        const duration = Date.now() - startTime;
        
        logAdminAction(
          request,
          options.action,
          options.resource,
          undefined,
          {
            duration,
            statusCode: response.status,
          },
          true
        ).catch(error => {
          console.error('Failed to log activity:', error);
        });
      }
      
      return response;
    } catch (error) {
      // Log failed request
      if (options?.action && options?.resource) {
        const duration = Date.now() - startTime;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        
        logAdminAction(
          request,
          options.action,
          options.resource,
          undefined,
          {
            duration,
            error: errorMessage,
          },
          false,
          errorMessage
        ).catch(logError => {
          console.error('Failed to log activity error:', logError);
        });
      }
      
      throw error;
    }
  };
}

/**
 * Rate limiting for activity logging to prevent spam
 */
const logRateLimit = new Map<string, number>();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const MAX_LOGS_PER_WINDOW = 100;

export function shouldRateLimit(identifier: string): boolean {
  const now = Date.now();
  const windowStart = now - RATE_LIMIT_WINDOW;
  
  // Clean up old entries
  for (const [key, timestamp] of logRateLimit.entries()) {
    if (timestamp < windowStart) {
      logRateLimit.delete(key);
    }
  }
  
  // Count logs in current window
  const logsInWindow = Array.from(logRateLimit.values())
    .filter(timestamp => timestamp > windowStart).length;
  
  if (logsInWindow >= MAX_LOGS_PER_WINDOW) {
    return true;
  }
  
  logRateLimit.set(identifier, now);
  return false;
}

/**
 * Batch logging for high-volume activities
 */
interface BatchLogEntry {
  userId?: string;
  adminId?: string;
  action: string;
  resource?: string;
  resourceId?: string;
  details?: any;
  success: boolean;
  errorMessage?: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
}

class BatchLogger {
  private batch: BatchLogEntry[] = [];
  private batchSize = 50;
  private flushInterval = 30000; // 30 seconds
  private timer: NodeJS.Timeout | null = null;

  constructor() {
    this.startTimer();
  }

  add(entry: Omit<BatchLogEntry, 'timestamp'>) {
    this.batch.push({
      ...entry,
      timestamp: new Date(),
    });

    if (this.batch.length >= this.batchSize) {
      this.flush();
    }
  }

  private async flush() {
    if (this.batch.length === 0) return;

    const entries = [...this.batch];
    this.batch = [];

    try {
      const { db } = await import('@/lib/prisma');
      
      await db.activityLog.createMany({
        data: entries.map(entry => ({
          userId: entry.userId || null,
          adminId: entry.adminId || null,
          action: entry.action,
          resource: entry.resource || null,
          resourceId: entry.resourceId || null,
          details: entry.details || null,
          success: entry.success,
          errorMessage: entry.errorMessage || null,
          ipAddress: entry.ipAddress || null,
          userAgent: entry.userAgent || null,
          timestamp: entry.timestamp,
        })),
      });
    } catch (error) {
      console.error('Failed to flush batch logs:', error);
      // Re-add entries to batch for retry
      this.batch.unshift(...entries);
    }
  }

  private startTimer() {
    this.timer = setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }

  stop() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    this.flush(); // Flush remaining entries
  }
}

export const batchLogger = new BatchLogger();

// Graceful shutdown
if (typeof process !== 'undefined') {
  process.on('SIGTERM', () => batchLogger.stop());
  process.on('SIGINT', () => batchLogger.stop());
}
