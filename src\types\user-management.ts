import { UserRole } from '@prisma/client';

export interface User {
  id: string;
  name: string | null;
  email: string | null;
  role: UserRole;
  emailVerified: string | null;
  createdAt: string;
  updatedAt: string;
  image?: string | null;
  verificationStatus?: string | null;
  isVerified?: boolean;
  verificationDate?: string | null;
  verificationSubmittedAt?: string | null;
  accountBalance?: number;
  eventsCount?: number;
  accountsCount?: number;
  isActive?: boolean;
}

export interface CreateUserData {
  name: string;
  email: string;
  role: UserRole;
}

export interface UpdateUserData {
  name?: string;
  role?: UserRole;
  emailVerified?: boolean;
}

export interface UserInvitation {
  id: string;
  email: string;
  role: UserRole;
  status: 'PENDING' | 'ACCEPTED' | 'DECLINED' | 'EXPIRED';
  expiresAt: string;
  createdAt: string;
  invitedBy: string;
}

export interface ActivityLog {
  id: string;
  userId?: string | null;
  adminId?: string | null;
  action: string;
  resource?: string | null;
  resourceId?: string | null;
  details?: any;
  ipAddress?: string | null;
  userAgent?: string | null;
  timestamp: string;
  success: boolean;
  errorMessage?: string | null;
  admin?: {
    id: string;
    name: string | null;
    email: string | null;
    role: UserRole;
  } | null;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface ApiResponse<T> {
  data?: T;
  error?: string;
  details?: string;
  message?: string;
}

export interface UsersResponse {
  users: User[];
  pagination: PaginationInfo;
}

export interface InvitationsResponse {
  invitations: UserInvitation[];
  pagination: PaginationInfo;
}

export interface ActivityLogsResponse {
  logs: ActivityLog[];
  pagination: PaginationInfo;
  summary: {
    totalLogs: number;
    successfulActions: number;
    failedActions: number;
    uniqueAdmins: number;
  };
}

export interface CreateUserResponse {
  user: User;
  temporaryPassword: string;
  message: string;
}

export interface InviteUserData {
  email: string;
  role: UserRole;
  name?: string;
}

export interface InviteUserResponse {
  message: string;
  invitation: UserInvitation;
}

// Filter and sort options
export interface UserFilters {
  search?: string;
  role?: UserRole | 'all';
  verification?: 'verified' | 'unverified' | 'all';
  page?: number;
  limit?: number;
  sortField?: string;
  sortDirection?: 'asc' | 'desc';
}

export interface ActivityLogFilters {
  action?: string;
  resource?: string;
  adminId?: string;
  success?: boolean | null;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Form validation types
export interface FormErrors {
  [key: string]: string | undefined;
}

export interface UserFormData {
  name: string;
  email: string;
  role: UserRole;
}

export interface InviteFormData {
  email: string;
  role: UserRole;
  name: string;
}

// Component props types
export interface UserTableProps {
  users: User[];
  loading: boolean;
  onEdit: (user: User) => void;
  onDelete: (user: User) => void;
  onViewDetails: (user: User) => void;
  onRoleChange: (userId: string, newRole: UserRole) => void;
}

export interface UserFormProps {
  user?: User;
  onSubmit: (data: UserFormData) => void;
  onCancel: () => void;
  loading: boolean;
  errors: FormErrors;
}

export interface InviteUserFormProps {
  onSubmit: (data: InviteFormData) => void;
  onCancel: () => void;
  loading: boolean;
  errors: FormErrors;
}

export interface ActivityLogTableProps {
  logs: ActivityLog[];
  loading: boolean;
  onRefresh: () => void;
}

export interface UserStatsProps {
  totalUsers: number;
  activeUsers: number;
  pendingInvitations: number;
  recentActivity: number;
}

// Role configuration
export const USER_ROLES: { value: UserRole; label: string; description: string }[] = [
  {
    value: 'USER',
    label: 'User',
    description: 'Regular platform user with basic access'
  },
  {
    value: 'ORGANIZER',
    label: 'Organizer',
    description: 'Can create and manage events'
  },
  {
    value: 'VENDOR',
    label: 'Vendor',
    description: 'Can sell products and services at events'
  },
  {
    value: 'ADMIN',
    label: 'Admin',
    description: 'Platform administrator with management access'
  },
  {
    value: 'SUPERADMIN',
    label: 'Super Admin',
    description: 'Full platform access and control'
  },
  {
    value: 'DEVELOPER',
    label: 'Developer',
    description: 'Technical access for development and debugging'
  },
  {
    value: 'PARTNER',
    label: 'Partner',
    description: 'Business partner with special privileges'
  },
];

// Action types for activity logs
export const ACTIVITY_ACTIONS = [
  'CREATE_USER',
  'UPDATE_USER',
  'DELETE_USER',
  'INVITE_USER',
  'ACCEPT_INVITATION',
  'VIEW_USERS',
  'VIEW_ACTIVITY_LOGS',
  'UNAUTHORIZED_ACCESS_ATTEMPT',
  'LOGIN_SUCCESS',
  'LOGIN_FAILED',
  'PASSWORD_RESET',
  'ROLE_CHANGE',
] as const;

export type ActivityAction = typeof ACTIVITY_ACTIONS[number];

// Resource types for activity logs
export const ACTIVITY_RESOURCES = [
  'users',
  'user_invitations',
  'activity_logs',
  'events',
  'orders',
  'products',
  'settings',
] as const;

export type ActivityResource = typeof ACTIVITY_RESOURCES[number];
